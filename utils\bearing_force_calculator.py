"""
支座恢复力计算模块

该模块提供专业的支座恢复力计算功能，考虑：
1. 橡胶支座的双线性本构关系
2. 摩擦滑动支座的弹塑性行为
3. 支座轴向荷载对水平刚度的影响

作者: 桥梁工程师
日期: 2025-08-16
"""

import numpy as np
from typing import Dict, List, Tuple, Optional


class BearingForceCalculator:
    """支座恢复力计算器"""
    
    def __init__(self, bearing_params: Dict, axial_loads: Dict = None):
        """
        初始化支座恢复力计算器
        
        参数:
            bearing_params: 支座参数字典
            axial_loads: 支座轴向荷载字典 {elem_tag: axial_load}
        """
        self.bearing_params = bearing_params
        self.axial_loads = axial_loads or {}
        
    def calculate_rubber_bearing_force(self, displacement: float, elem_tag: int = None) -> float:
        """
        计算橡胶支座恢复力（双线性模型）
        
        参数:
            displacement: 水平位移 (m)
            elem_tag: 支座元素标签
            
        返回:
            float: 恢复力 (N)
        """
        # 获取支座参数
        stiffness = self.bearing_params.get('stiffness', {})
        k1 = stiffness.get('horizontal', 1e8)  # 初始刚度 (N/m)
        
        # 获取屈服位移
        yield_disp = self.bearing_params.get('yield_displacement', 0.005)  # m
        
        # 屈服后刚度比
        alpha = self.bearing_params.get('post_yield_stiffness_ratio', 0.1)
        k2 = alpha * k1  # 屈服后刚度
        
        # 计算屈服力
        fy = k1 * yield_disp
        
        # 双线性计算
        abs_disp = abs(displacement)
        if abs_disp <= yield_disp:
            # 弹性阶段
            force = k1 * displacement
        else:
            # 屈服后阶段
            force_sign = np.sign(displacement)
            force = force_sign * (fy + k2 * (abs_disp - yield_disp))
            
        return force
        
    def calculate_friction_bearing_force(self, displacement: float, velocity: float = 0.0, 
                                       elem_tag: int = None) -> float:
        """
        计算摩擦滑动支座恢复力
        
        参数:
            displacement: 水平位移 (m)
            velocity: 水平速度 (m/s)
            elem_tag: 支座元素标签
            
        返回:
            float: 恢复力 (N)
        """
        # 获取轴向荷载
        axial_load = self.axial_loads.get(elem_tag, 0.0) if elem_tag else 0.0
        
        # 摩擦系数
        mu_s = self.bearing_params.get('friction_coefficient_static', 0.05)  # 静摩擦系数
        mu_k = self.bearing_params.get('friction_coefficient_kinetic', 0.03)  # 动摩擦系数
        
        # 速度阈值
        v_threshold = self.bearing_params.get('velocity_threshold', 1e-6)  # m/s
        
        # 弹性刚度（滑动前）
        k_elastic = self.bearing_params.get('stiffness', {}).get('horizontal', 1e8)
        
        # 滑动位移阈值
        disp_threshold = mu_s * axial_load / k_elastic if k_elastic > 0 else 0.0
        
        abs_disp = abs(displacement)
        abs_vel = abs(velocity)
        
        if abs_disp <= disp_threshold:
            # 弹性阶段（未滑动）
            force = k_elastic * displacement
        else:
            # 滑动阶段
            if abs_vel > v_threshold:
                # 动摩擦
                friction_force = mu_k * axial_load
            else:
                # 静摩擦
                friction_force = mu_s * axial_load
                
            force = np.sign(displacement) * friction_force
            
        return force
        
    def calculate_bilinear_bearing_force(self, displacement: float, elem_tag: int = None) -> float:
        """
        计算双线性支座恢复力（考虑轴向荷载影响）
        
        参数:
            displacement: 水平位移 (m)
            elem_tag: 支座元素标签
            
        返回:
            float: 恢复力 (N)
        """
        # 获取轴向荷载
        axial_load = self.axial_loads.get(elem_tag, 0.0) if elem_tag else 0.0
        
        # 基础刚度参数
        base_stiffness = self.bearing_params.get('stiffness', {}).get('horizontal', 1e8)
        
        # 轴向荷载对刚度的影响系数
        axial_effect = self.bearing_params.get('axial_load_effect', 1.0)
        
        # 修正后的刚度
        if axial_load > 0:
            k1 = base_stiffness * (1 + axial_effect * axial_load / 1e6)  # 考虑轴向荷载影响
        else:
            k1 = base_stiffness
            
        # 屈服参数
        yield_disp = self.bearing_params.get('yield_displacement', 0.005)  # m
        alpha = self.bearing_params.get('post_yield_stiffness_ratio', 0.1)
        k2 = alpha * k1
        
        # 计算恢复力
        abs_disp = abs(displacement)
        if abs_disp <= yield_disp:
            force = k1 * displacement
        else:
            fy = k1 * yield_disp
            force_sign = np.sign(displacement)
            force = force_sign * (fy + k2 * (abs_disp - yield_disp))
            
        return force
        
    def calculate_force_time_history(self, displacements: np.ndarray, times: np.ndarray,
                                    elem_tag: int = None, bearing_type: str = 'rubber') -> np.ndarray:
        """
        计算支座恢复力时程
        
        参数:
            displacements: 位移时程数组 (m)
            times: 时间数组 (s)
            elem_tag: 支座元素标签
            bearing_type: 支座类型 ('rubber', 'friction', 'bilinear')
            
        返回:
            np.ndarray: 恢复力时程数组 (N)
        """
        forces = np.zeros_like(displacements)
        
        # 计算速度（用于摩擦支座）
        velocities = np.zeros_like(displacements)
        if len(times) > 1:
            velocities[1:] = np.diff(displacements) / np.diff(times)
            
        # 根据支座类型计算恢复力
        for i, disp in enumerate(displacements):
            vel = velocities[i] if i < len(velocities) else 0.0
            
            if bearing_type == 'rubber':
                forces[i] = self.calculate_rubber_bearing_force(disp, elem_tag)
            elif bearing_type == 'friction':
                forces[i] = self.calculate_friction_bearing_force(disp, vel, elem_tag)
            elif bearing_type == 'bilinear':
                forces[i] = self.calculate_bilinear_bearing_force(disp, elem_tag)
            else:
                # 默认线性弹性
                k = self.bearing_params.get('stiffness', {}).get('horizontal', 1e8)
                forces[i] = k * disp
                
        return forces
        
    def get_bearing_properties(self, elem_tag: int = None) -> Dict:
        """
        获取支座特性参数
        
        参数:
            elem_tag: 支座元素标签
            
        返回:
            dict: 支座特性字典
        """
        properties = {}
        
        # 基本刚度
        stiffness = self.bearing_params.get('stiffness', {})
        properties['horizontal_stiffness'] = stiffness.get('horizontal', 1e8)
        properties['vertical_stiffness'] = stiffness.get('vertical', 1e10)
        
        # 屈服参数
        properties['yield_displacement'] = self.bearing_params.get('yield_displacement', 0.005)
        properties['yield_force'] = properties['horizontal_stiffness'] * properties['yield_displacement']
        
        # 轴向荷载
        if elem_tag and elem_tag in self.axial_loads:
            properties['axial_load'] = self.axial_loads[elem_tag]
        else:
            properties['axial_load'] = 0.0
            
        # 摩擦参数（如果适用）
        if self.bearing_params.get('friction', False):
            properties['friction_coefficient'] = self.bearing_params.get('friction_coefficient_static', 0.05)
            properties['friction_force'] = properties['friction_coefficient'] * properties['axial_load']
            
        return properties


def create_bearing_calculator_from_model(model) -> BearingForceCalculator:
    """
    从桥梁模型创建支座恢复力计算器
    
    参数:
        model: 桥梁模型对象
        
    返回:
        BearingForceCalculator: 支座恢复力计算器
    """
    # 获取支座参数
    bearing_params = model.params.bearing
    
    # 获取支座轴向荷载
    axial_loads = getattr(model, 'bearing_axial_loads', {})
    
    return BearingForceCalculator(bearing_params, axial_loads)


def calculate_bearing_hysteresis_properties(displacements: np.ndarray, forces: np.ndarray) -> Dict:
    """
    计算滞回曲线特性参数
    
    参数:
        displacements: 位移数组 (m)
        forces: 力数组 (N)
        
    返回:
        dict: 滞回特性参数
    """
    properties = {}
    
    # 最大值
    properties['max_displacement'] = np.max(np.abs(displacements))
    properties['max_force'] = np.max(np.abs(forces))
    
    # 初始刚度（线性回归前10%数据点）
    n_points = len(displacements)
    n_initial = max(int(0.1 * n_points), 2)
    
    if n_initial < n_points:
        # 选择接近原点的数据点
        indices = np.argsort(np.abs(displacements))[:n_initial]
        disp_initial = displacements[indices]
        force_initial = forces[indices]
        
        if len(disp_initial) > 1 and np.std(disp_initial) > 1e-10:
            properties['initial_stiffness'] = np.polyfit(disp_initial, force_initial, 1)[0]
        else:
            properties['initial_stiffness'] = 0.0
    else:
        properties['initial_stiffness'] = 0.0
    
    # 能量耗散（滞回环面积）
    if len(displacements) > 2:
        # 使用梯形法则计算环路积分
        energy_dissipated = 0.0
        for i in range(1, len(displacements)):
            energy_dissipated += 0.5 * (forces[i] + forces[i-1]) * (displacements[i] - displacements[i-1])
        properties['energy_dissipated'] = abs(energy_dissipated)
    else:
        properties['energy_dissipated'] = 0.0
    
    # 等效阻尼比
    if properties['max_displacement'] > 0 and properties['max_force'] > 0:
        elastic_energy = 0.5 * properties['initial_stiffness'] * properties['max_displacement']**2
        if elastic_energy > 0:
            properties['equivalent_damping_ratio'] = properties['energy_dissipated'] / (4 * np.pi * elastic_energy)
        else:
            properties['equivalent_damping_ratio'] = 0.0
    else:
        properties['equivalent_damping_ratio'] = 0.0
    
    return properties
