import openseespy.opensees as ops


def set_concrete(params, mat_tags):        
    """设置基本混凝土材料模型"""
    # 保护层混凝土（非约束）
    set_unconfined_concrete(
        mat_tag=mat_tags["ConcreteCover"],
        concrete_params=params.concrete_materials["cover"]
    )

    # 核心区混凝土（约束）
    set_confined_concrete(
        mat_tag=mat_tags["ConcreteCore"],
        params=params,
        concrete_params=params.concrete_materials["core"],
        transverse_params=params.pier_section["transverse_bars"],
        steel_params=params.steel_materials[params.pier_section["transverse_bars"]["material"]]
    )


def set_unconfined_concrete(mat_tag, concrete_params):
    """设置非约束混凝土材料模型（保护层）"""
    fpc = -concrete_params["fc"] * 1e6  # 峰值压应力（负值，Pa）
    epsc0 = -concrete_params["ec"]      # 峰值压应变（负值）
    fpcu = fpc * 0.3                    # 极限压应力（取峰值的30%）
    epscu = -concrete_params["ecu"]     # 极限压应变（负值）
    ft = concrete_params["ft"] * 1e6    # 抗拉强度（Pa）
    Ets = concrete_params["Et"] * 1e6   # 受拉软化模量

    # 创建Concrete02材料
    ops.uniaxialMaterial('Concrete02', mat_tag,
                        fpc,                   # 峰值压应力
                        epsc0,                 # 峰值压应变
                        fpcu,                  # 极限压应力
                        epscu,                 # 极限压应变
                        0.1,                   # lambda 卸载参数
                        ft,                    # 抗拉强度
                        Ets)                   # Ets 刚度衰减(控制受拉软化)


def set_confined_concrete(mat_tag, params, concrete_params, transverse_params, steel_params):
    """设置约束混凝土材料模型（核心区）

    基于Mander模型计算约束效应
    """
    # 基本参数
    fc = concrete_params["fc"] * 1e6    # 混凝土抗压强度（Pa）
    ec = concrete_params["ec"]          # 峰值应变
    k = concrete_params["k"]            # 强度提高系数

    # 箍筋参数
    transverse_dia = transverse_params["diameter"]  # 箍筋直径(m)
    transverse_spacing = transverse_params["spacing"]  # 箍筋间距(m)
    fy_t = steel_params["fy"] * 1e6     # 箍筋屈服强度（Pa）

    # 计算约束效应
    rho_s = transverse_params["rho"]
    if transverse_params["configuration"] == "spiral":  # 螺旋箍筋
        ke = 1.0  # 螺旋箍筋的有效约束系数
    else:  # 环形箍筋
        ke = 0.75  # 环形箍筋的有效约束系数

    # 计算侧向约束压力
    fl = 0.5 * ke * rho_s * fy_t

    # 计算约束混凝土强度提高系数（Mander模型）
    k_actual = 1.0 + 3.7 * (fl / fc)**0.86

    # 使用计算值和参数值中的较小值，避免过度估计
    k_confined = min(k, k_actual)
    print(f"约束混凝土强度提高系数: {k_confined:.2f}")
    

    # 计算约束混凝土参数
    fcc = -k_confined * fc              # 约束混凝土峰值压应力（负值，Pa）
    ecc = -ec * (1 + 5 * (k_confined - 1))  # 约束混凝土峰值压应变（负值）

    # 极限参数
    fpcu = 0.2 * fcc                    # 极限压应力（取峰值的20%）
    epscu = -concrete_params["ecu"]     # 极限压应变（负值）

    # 抗拉参数（与非约束混凝土相同）
    ft = concrete_params["ft"] * 1e6    # 抗拉强度（Pa）
    Ets = concrete_params["Et"] * 1e6   # 受拉软化模量

    # 创建Concrete02材料
    ops.uniaxialMaterial('Concrete02', mat_tag,
                        fcc,                   # 峰值压应力
                        ecc,                   # 峰值压应变
                        fpcu,                  # 极限压应力
                        epscu,                 # 极限压应变
                        0.1,                   # lambda 卸载参数
                        ft,                    # 抗拉强度
                        Ets)                   # Ets 刚度衰减(控制受拉软化)
