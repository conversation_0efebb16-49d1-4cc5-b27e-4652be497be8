"""
Simply Supported Bridge Isolator Module

This module handles the creation and configuration of rubber bearings
connecting cap beams to main beams.
"""

import openseespy.opensees as ops
import numpy as np
from components.collision import add_collision_elements


def create_isolators(model):
    """Create rubber bearing elements between cap beams and deck"""
    # Case 1: With piers - connect deck to cap beams
    if model.cap_beams['nodes']:
        create_pier_bearings(model)
    else:
        print("Warning: No cap beam nodes to connect with deck")
        return

    # Case 2: Without piers or at abutments - create direct supports
    create_abutment_bearings(model)

    # Create the actual bearing elements
    create_bearing_elements(model)

    # Add transverse blocks to constrain y-direction displacement
    if model.params.block_transverse:
        add_transverse_blocks(model)

    # Add collision elements between deck nodes and abutment nodes
    add_collision_elements(model)


def create_pier_bearings(model):
    """Create bearing connections at pier locations with multiple bearings per cap beam"""
    for pier_long_idx, cap_nodes in model.cap_beams['nodes'].items():
        # Get adjacent spans
        span_before = pier_long_idx
        span_after = pier_long_idx + 1

        # Skip if either span doesn't exist
        if span_before not in model.span_nodes or span_after not in model.span_nodes:
            continue

        # Get end node of span before and start node of span after
        end_node_before = model.span_nodes[span_before]['end']
        start_node_after = model.span_nodes[span_after]['start']

        # Get deck coordinates for reference
        deck_x = ops.nodeCoord(end_node_before, 1)  # Same x-coord for both deck nodes
        deck_z = ops.nodeCoord(end_node_before, 3)  # Elevation of deck

        # Calculate bearing positions based on number of bearings per cap beam
        bearing_spacing = model.params.bearing_spacing
        num_bearings = int(model.params.deck_width // bearing_spacing)

        # Calculate bearing positions (symmetric about cap beam center)
        total_width = bearing_spacing * (num_bearings - 1)
        bearing_y_offsets = np.linspace(-total_width/2, total_width/2, num_bearings)

        # Create bearing nodes and connections for the span before
        for y_offset in bearing_y_offsets:
            # Create a node on the deck at the bearing position
            deck_bearing_node = model._next_tag('node')
            ops.node(deck_bearing_node, deck_x, y_offset, deck_z)

            # Find the closest cap beam node to connect to
            closest_cap_node = None
            min_distance = float('inf')

            for cap_node in cap_nodes:
                cap_y = ops.nodeCoord(cap_node, 2)
                distance = abs(cap_y - y_offset)
                if distance < min_distance:
                    min_distance = distance
                    closest_cap_node = cap_node

            # Create a rigid link between the deck node and the bearing node
            # This ensures the deck node moves with the main deck and transfers forces
            ops.rigidLink('beam', end_node_before, deck_bearing_node)
            # ops.equalDOF(end_node_before, deck_bearing_node, 1, 2, 3, 4, 5, 6)

            # Add the bearing connection
            model.bearings['spans'].append(span_before)
            model.bearings['connections'].append((deck_bearing_node, closest_cap_node))

        # Create bearing nodes and connections for the span after
        for y_offset in bearing_y_offsets:
            # Create a node on the deck at the bearing position
            deck_bearing_node = model._next_tag('node')
            ops.node(deck_bearing_node, deck_x, y_offset, deck_z)

            # Find the closest cap beam node to connect to
            closest_cap_node = None
            min_distance = float('inf')

            for cap_node in cap_nodes:
                cap_y = ops.nodeCoord(cap_node, 2)
                distance = abs(cap_y - y_offset)
                if distance < min_distance:
                    min_distance = distance
                    closest_cap_node = cap_node

            # Create a rigid link between the deck node and the bearing node
            # This ensures the deck node moves with the main deck and transfers forces
            ops.rigidLink('beam', start_node_after, deck_bearing_node)
            # ops.equalDOF(start_node_after, deck_bearing_node, 1, 2, 3, 4, 5, 6)

            # Add the bearing connection
            model.bearings['spans'].append(span_after)
            model.bearings['connections'].append((deck_bearing_node, closest_cap_node))


def create_abutment_bearings(model):
    """Create bearing connections at abutments or for spans without piers"""
    # Helper function to create multiple bearings at a deck node
    def create_multiple_bearings(deck_node, span_idx):
        # Get deck coordinates
        x = ops.nodeCoord(deck_node, 1)
        z = ops.nodeCoord(deck_node, 3)

        # will link with soil node
        abutment_node = model._next_tag('node')
        ops.node(abutment_node, x, 0, z)
        model.abutments['nodes'].append(abutment_node)

        # Calculate bearing positions based on number of bearings
        bearing_spacing = model.params.bearing_spacing
        num_bearings = int(model.params.deck_width // bearing_spacing)

        # Calculate bearing positions (symmetric about deck center)
        total_width = bearing_spacing * (num_bearings - 1)
        bearing_y_offsets = np.linspace(-total_width/2, total_width/2, num_bearings)

        # Check if this node is already connected to a cap beam
        connected_bearings = []
        for bearing_node, _ in model.bearings['connections']:
            if ops.nodeCoord(bearing_node, 1) == x:  # Same x-coordinate
                connected_bearings.append(bearing_node)

        # If already has bearings, skip
        if connected_bearings:
            return

        # Create bearing nodes at each offset position
        for y_offset in bearing_y_offsets:
            # Create a node on the deck at the bearing position
            deck_bearing_node = model._next_tag('node')
            ops.node(deck_bearing_node, x, y_offset, z)

            # Create a abutment node at the same location
            abutment_bearing_node = model._next_tag('node')
            ops.node(abutment_bearing_node, x, y_offset, z)

            ops.rigidLink('beam', deck_node, deck_bearing_node)
            ops.rigidLink('beam', abutment_node, abutment_bearing_node)

            # Add the bearing connection
            model.bearings['spans'].append(span_idx)
            model.bearings['connections'].append((deck_bearing_node, abutment_bearing_node))


    # Process first span - connect start to ground
    start_node = model.span_nodes[0]['start']
    create_multiple_bearings(start_node, 0)

    # Process last span - connect end to ground
    last_span_idx = max(model.span_nodes.keys())
    end_node = model.span_nodes[last_span_idx]['end']
    create_multiple_bearings(end_node, last_span_idx)


def create_bearing_elements(model):
    """创建支座单元"""

    # 创建支座元素
    for deck_node, support_node in model.bearings['connections']:
        elem_tag = model._next_tag('element')

        # Define materials for all 6 DOFs
        # Translational DOFs (1,2,3 = X,Y,Z)
        # Rotational DOFs (4,5,6 = rotation about X,Y,Z)
        ops.element('zeroLength',
            elem_tag, deck_node, support_node,
            '-mat',
            model.mat_tags['RubberX'],   # DOF 1 (X translation)
            model.mat_tags['RubberX'],   # DOF 2 (Y translation)
            model.mat_tags['RubberZ'],   # DOF 3 (Z translation)
            model.mat_tags['RubberRxy'], # DOF 4 (rotation about X)
            model.mat_tags['RubberRxy'], # DOF 5 (rotation about Y)
            model.mat_tags['RubberRz'],  # DOF 6 (rotation about Z)
            '-dir', 1, 2, 3, 4, 5, 6
        )

        # # 使用flatSliderBearing元素
        # ops.element('flatSliderBearing',
        #     elem_tag, deck_node, support_node,
        #     model.frn_tags["RubberFriction"],  # 摩擦模型
        #     model.params.bearing_kInit,        # 初始侧向刚度
        #     '-P', model.mat_tags["RubberZ"],   # 竖向材料
        #     '-T', model.mat_tags["RubberRz"],
        #     '-My', model.mat_tags["RubberRxy"],
        #     '-Mz', model.mat_tags["RubberRxy"],
        #     '-orient', 0, 0, 1, 1, 1, 0
        # )
        model.bearings['elements'].append(elem_tag)


def add_transverse_blocks(model):
    """
    添加横向挡块约束，使梁端节点和相邻盖梁节点的y方向位移相同

    这个函数通过在梁端节点和盖梁两端y方向最远的节点之间添加y方向的位移约束，
    模拟横向挡块的作用，防止梁在横桥向相对于盖梁的位移。
    """
    # 如果没有盖梁节点，则无需添加横向挡块
    if not model.cap_beams['nodes']:
        return

    # 创建一个字典来存储已经添加了约束的节点对，避免重复添加
    constrained_pairs = set()

    # 遍历每个纵向位置的盖梁
    for pier_long_idx, cap_nodes in model.cap_beams['nodes'].items():
        # 跳过没有节点的盖梁
        if not cap_nodes or len(cap_nodes) < 2:
            continue

        # 获取盖梁两端的节点（y方向最小和最大的节点）
        # 由于cap_nodes已经按y坐标排序，第一个和最后一个节点就是两端节点
        cap_node_min_y = cap_nodes[0]
        cap_node_max_y = cap_nodes[-1]

        # 获取盖梁的x坐标（所有节点x坐标相同）
        cap_x = ops.nodeCoord(cap_node_min_y, 1)

        # 找到与此盖梁相连的梁端节点
        # 获取相邻的跨
        span_before = pier_long_idx
        span_after = pier_long_idx + 1

        # 处理前一跨的末端节点
        if span_before in model.span_nodes:
            end_node_before = model.span_nodes[span_before]['end']

            # 检查x坐标是否匹配
            if np.isclose(ops.nodeCoord(end_node_before, 1), cap_x):
                # 为盖梁两端节点添加约束
                for cap_end_node in [cap_node_min_y, cap_node_max_y]:
                    # 创建一个唯一的键来表示这对节点
                    node_pair = tuple(sorted([end_node_before, cap_end_node]))

                    # 如果这对节点还没有添加约束，则添加
                    if node_pair not in constrained_pairs:
                        # 添加y方向(DOF 2)的位移约束
                        ops.equalDOF(end_node_before, cap_end_node, 2)
                        constrained_pairs.add(node_pair)

                        # 打印信息
                        girder_y = ops.nodeCoord(end_node_before, 2)
                        cap_y = ops.nodeCoord(cap_end_node, 2)
                        # print(f"添加横向挡块约束: 梁端节点 {end_node_before} (y={girder_y:.3f}) 与 盖梁端部节点 {cap_end_node} (y={cap_y:.3f})")

        # 处理后一跨的起始节点
        if span_after in model.span_nodes:
            start_node_after = model.span_nodes[span_after]['start']

            # 检查x坐标是否匹配
            if np.isclose(ops.nodeCoord(start_node_after, 1), cap_x):
                # 为盖梁两端节点添加约束
                for cap_end_node in [cap_node_min_y, cap_node_max_y]:
                    # 创建一个唯一的键来表示这对节点
                    node_pair = tuple(sorted([start_node_after, cap_end_node]))

                    # 如果这对节点还没有添加约束，则添加
                    if node_pair not in constrained_pairs:
                        # 添加y方向(DOF 2)的位移约束
                        ops.equalDOF(start_node_after, cap_end_node, 2)
                        constrained_pairs.add(node_pair)

                        # 打印信息
                        girder_y = ops.nodeCoord(start_node_after, 2)
                        cap_y = ops.nodeCoord(cap_end_node, 2)
                        # print(f"添加横向挡块约束: 梁端节点 {start_node_after} (y={girder_y:.3f}) 与 盖梁端部节点 {cap_end_node} (y={cap_y:.3f})")

    # 打印总结信息
    # print(f"共添加了 {len(constrained_pairs)} 个横向挡块约束")

