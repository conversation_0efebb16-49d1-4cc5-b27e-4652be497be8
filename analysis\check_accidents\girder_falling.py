"""
落梁事故检查模块

该模块用于检查桥梁在地震作用下是否发生落梁事故。
根据规范CJJ 166-2011城市桥梁抗震设计规范, 当支座相对位移超过限值时, 
认为发生落梁事故。
"""

# import os
# import csv
# import numpy as np
# from analysis.utils.data_readers import read_bearing_displacements


def check_girder_falling(bearing_data, model=None, params=None):
    """检查是否发生落梁事故

    根据支座相对位移数据和落梁限值，检查是否发生落梁事故。
    对于桥台支座, 左桥台支座只有沿x轴正向滑动才发生落梁, 
    右桥台支座只有沿x轴反向滑动才发生落梁, 桥墩支座两个方向都可能发生落梁。

    参数:
        bearing_data: 支座相对位移数据字典
            格式: {time: [bearing_info_dict, ...]}
        model: 桥梁模型对象，用于获取支座屈服位移信息
        params: 桥梁参数对象，用于获取落梁限值

    返回:
        bool: 是否发生落梁事故
    """
    # 检查模型和参数
    if model is None and params is None:
        print("错误: 必须提供模型对象或参数对象")
        return False

    # 获取落梁限值
    if params is None:
        params = model.params
    girder_falling_limits = params.accidents.get("girder_falling", [])
    if not girder_falling_limits:
        print("警告: 未找到落梁限值，无法检查落梁事故")
        return False

    # 获取支座屈服位移信息
    bearing_yield_disps = {}
    if hasattr(params, 'bearing_materials') and params.bearing_materials:
        for elem_tag, info in params.bearing_materials.items():
            bearing_yield_disps[elem_tag] = info['yield_disp']
    else:
        print("警告: 未找到支座屈服位移信息, 将使用0作为屈服位移")

    # # 读取支座相对位移数据
    # bearing_data = read_bearing_displacements(bearing_disps_file, model)
    # if not bearing_data:
    #     print(f"错误: 无法从 {bearing_disps_file} 读取支座相对位移数据")
    #     return False

    # 确定左右桥台位置
    min_x = min(data['x_coord'] for time_data in bearing_data.values()
                for data in time_data)
    max_x = max(data['x_coord'] for time_data in bearing_data.values()
                for data in time_data)

    # 检查每个时间步的支座相对位移
    accident_time = float('inf')
    accident_bearing = None

    for time, time_data in sorted(bearing_data.items()):
        for bearing in time_data:
            span = bearing['span']
            x_coord = bearing['x_coord']
            y_coord = bearing['y_coord']
            bearing_idx = bearing['bearing_idx']
            elem_tag = bearing.get('elem_tag')

            # 获取相对位移 (mm -> m)
            rel_disp_x = bearing['rel_disp_x']  # 传入原始数据单位为m, csv文件中为mm

            # 获取屈服位移
            yield_disp = bearing_yield_disps.get(elem_tag, 0.0) if elem_tag else 0.0

            # # 计算滑动距离 (相对位移绝对值 - 屈服位移)
            # sliding_distance = abs(rel_disp_x) - yield_disp
            sliding_distance = abs(rel_disp_x)

            # 获取该跨的落梁限值
            limit = girder_falling_limits[span]

            # 判断是否发生落梁
            is_falling = False

            # 判断支座位置 (左桥台、右桥台或桥墩)
            is_left_abutment = abs(x_coord - min_x) < 0.1
            is_right_abutment = abs(x_coord - max_x) < 0.1

            if is_left_abutment:
                # 左桥台支座只有沿x轴正向滑动才发生落梁
                if rel_disp_x > 0 and sliding_distance > limit:
                    is_falling = True
            elif is_right_abutment:
                # 右桥台支座只有沿x轴反向滑动才发生落梁
                if rel_disp_x < 0 and sliding_distance > limit:
                    is_falling = True
            else:
                # 桥墩支座两个方向都可能发生落梁
                if sliding_distance > limit:
                    is_falling = True

            if is_falling and time < accident_time:
                accident_time = time
                accident_bearing = {
                    'time': time, 'span': span, 'bearing_idx': bearing_idx,
                    'x_coord': x_coord, 'y_coord': y_coord, 'rel_disp_x': rel_disp_x,
                    'yield_disp': yield_disp, 'sliding_distance': sliding_distance, 'limit': limit,
                    'location': 'left_abutment' if is_left_abutment else 'right_abutment' if is_right_abutment else 'pier'
                }

    # 输出结果
    if accident_bearing:
        print("\n发生落梁事故!")
        print(f"最早发生落梁的时刻: {accident_bearing['time']:.3f}s")
        print(f"落梁支座位置: {accident_bearing['location']}")
        print(f"落梁支座坐标: ({accident_bearing['x_coord']:.3f}, {accident_bearing['y_coord']:.3f})")
        print(f"落梁支座跨号: {accident_bearing['span']}")
        print(f"落梁支座编号: {accident_bearing['bearing_idx']}")
        print(f"落梁限值: {accident_bearing['limit']:.3f}m")
        print(f"相对位移: {accident_bearing['rel_disp_x']:.3f}m")
        print(f"屈服位移: {accident_bearing['yield_disp']:.3f}m")
        print(f"滑动距离: {accident_bearing['sliding_distance']:.3f}m")

        return True
    else:
        print("\n未发生落梁事故")
        return False


if __name__ == "__main__":
    # 测试函数
    from params import BridgeParams
    params = BridgeParams()
    check_girder_falling(params=params)
