"""
桥墩位移超限检查模块

该模块用于检查桥梁在地震作用下是否发生桥墩位移超限事故。
根据规范CJJ 166-2011城市桥梁抗震设计规范，当桥墩顶部相对位移超过限值时，
认为发生桥墩位移超限事故。
"""

import os
import numpy as np


def check_pier_displacement_accident(pier_relative_disps, model=None, params=None):
    """检查是否发生桥墩位移超限事故

    Args:
        pier_relative_disps: Dictionary of pier relative displacement data
        model: Bridge model object
        params: Bridge parameters object

    Returns:
        bool: True if pier displacement accident occurred, False otherwise
    """
    # 检查模型和参数
    if model is None and params is None:
        print("错误: 必须提供模型对象或参数对象")
        return False

    # 获取桥墩位移限值
    if params is None:
        params = model.params

    # 获取桥墩位移限值
    pier_disp_limit = params.accidents.get("pier_top_displacement")
    if pier_disp_limit is None:
        print("警告: 未找到桥墩位移限值，无法检查桥墩位移超限事故")
        return False

    # 检查每个时间步的桥墩相对位移
    accident_time = float('inf')
    accident_pier = None

    for time, time_data in sorted(pier_relative_disps.items()):
        for pier in time_data:
            pier_key = pier['pier_key']
            pier_long_idx = pier_key[0]
            pier_trans_idx = pier_key[1]
            x_coord = pier['x_coord']
            y_coord = pier['y_coord']

            # 获取相对位移
            rel_disp_x = pier['rel_disp_x']
            rel_disp_y = pier['rel_disp_y']

            # 计算水平位移合力 (x和y方向的合力)
            horizontal_disp = np.sqrt(rel_disp_x**2 + rel_disp_y**2)

            # 判断是否超限
            is_exceeding = horizontal_disp > pier_disp_limit

            if is_exceeding and time < accident_time:
                accident_time = time
                accident_pier = {
                    'time': time,
                    'pier_key': pier_key,
                    'pier_long_idx': pier_long_idx,
                    'pier_trans_idx': pier_trans_idx,
                    'x_coord': x_coord,
                    'y_coord': y_coord,
                    'rel_disp_x': rel_disp_x,
                    'rel_disp_y': rel_disp_y,
                    'horizontal_disp': horizontal_disp,
                    'limit': pier_disp_limit
                }

    # 输出结果
    if accident_pier:
        print("---------------------")
        print("发生桥墩位移超限事故!")
        print(f"最早发生超限的时刻: {accident_pier['time']:.3f}s")
        print(f"超限桥墩位置: ({accident_pier['pier_long_idx']},{accident_pier['pier_trans_idx']})")
        print(f"超限桥墩坐标: ({accident_pier['x_coord']:.3f}, {accident_pier['y_coord']:.3f})")
        print(f"位移限值: {accident_pier['limit']:.3f}m")
        print(f"X方向相对位移: {accident_pier['rel_disp_x']:.3f}m")
        print(f"Y方向相对位移: {accident_pier['rel_disp_y']:.3f}m")
        print(f"水平位移合力: {accident_pier['horizontal_disp']:.3f}m")

        return True
    else:
        print("---------------------")
        print("未发生桥墩位移超限事故")
        return False