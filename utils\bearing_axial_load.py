"""
支座轴向荷载计算模块

该模块用于计算支座轴向荷载，并根据轴向荷载更新支座材料属性。
"""

import openseespy.opensees as ops
from materials.rubber import set_rubber


def get_bearing_axial_loads(model):
    """获取支座轴向荷载并存储在model.bearing_axial_loads中

    return:
        dict: 支座轴向荷载字典 {elem_tag: axial_load}
    """
    # 初始化支座轴向荷载记录
    model.bearing_axial_loads = {}

    # 记录每个支座的轴向荷载
    total_axial_load = 0.0

    # 收集支座数据以便排序
    bearing_data = []

    for i, elem_tag in enumerate(model.bearings['elements']):
        # 获取单元连接的节点
        deck_node, support_node = model.bearings['connections'][i]

        # 获取支座轴向荷载，使用basicForce获取元素内力
        elem_forces = ops.basicForce(elem_tag)
        # 零长度元素的内力顺序: [Fx, Fy, Fz, Mx, My, Mz]
        axial_load = elem_forces[2]  # Z方向内力

        # 记录轴向荷载（取绝对值，确保为正值）
        axial_load = abs(axial_load)
        model.bearing_axial_loads[elem_tag] = axial_load
        total_axial_load += axial_load

        # 获取支座坐标
        x_coord = ops.nodeCoord(deck_node, 1)
        y_coord = ops.nodeCoord(deck_node, 2)

        # 获取支座所属的跨号
        # span_idx = bearing_spans.get(i, -1)  # 如果找不到，默认为-1
        span_idx = model.bearings['spans'][i]

        # 收集支座数据
        bearing_data.append({
            'elem_tag': elem_tag,
            'deck_node': deck_node,
            'support_node': support_node,
            'x_coord': x_coord,
            'y_coord': y_coord,
            'axial_load': axial_load,
            'span': span_idx
        })

    # 按X坐标、Y坐标和跨号排序
    bearing_data.sort(key=lambda x: (x['elem_tag'], x['x_coord'], x['y_coord']))

    # 输出详细支座信息
    # print("\n支座轴向荷载信息:")
    # print("支座编号\t跨号\tX坐标\tY坐标\t轴向荷载(kN)")
    # print("-" * 60)
    # for data in bearing_data:
    #     print(f"{data['elem_tag']}\t{data['span']}\t{data['x_coord']:.2f}\t{data['y_coord']:.2f}\t{data['axial_load']/1000:.2f}")
    # print(f"\n支座总轴向荷载: {total_axial_load/1000:.2f} kN")
    
    # 计算主梁总重量的估计值
    super_weight = model.params.super_weight
    
    # 检查总轴向荷载是否合理
    if abs(1 - total_axial_load / super_weight) > 0.5:
        print(f"支座总轴向荷载: {total_axial_load/1000:.2f} kN")
        print(f"主梁总重量: {super_weight/1000:.2f} kN")
        raise ValueError("支座总轴向荷载偏离主梁总重量超过50%")
    
    if total_axial_load < 1e-6:
        raise ValueError("支座总轴向荷载接近于零")

    # 恢复分析设置
    ops.wipeAnalysis()

    return model.bearing_axial_loads


def update_bearing_materials(model):
    """根据轴向荷载更新支座材料属性

    使用计算得到的轴向荷载，创建弹塑性材料模拟支座的摩擦滑动行为。

    参数:
        model: 桥梁模型对象
    """
    # 检查是否需要使用弹塑性材料
    if not model.params.bearing['friction']:
        return
    
    # 计算支座轴向荷载
    get_bearing_axial_loads(model)

    # 检查是否有轴向荷载数据
    if not hasattr(model, 'bearing_axial_loads') or not model.bearing_axial_loads:
        print("警告: 没有找到支座轴向荷载数据，无法创建弹塑性材料")
        return

    # 重新定义支座材料（使用弹塑性材料）
    set_rubber(model.params, model.mat_tags, model.bearing_axial_loads)

    # 更新支座元素的材料属性
    update_bearing_elements(model)


def update_bearing_elements(model):
    """更新支座元素的材料属性

    使用新创建的弹塑性材料更新支座元素。

    参数:
        model: 桥梁模型对象
    """
    # 检查是否有支座材料信息
    if not hasattr(model.params, 'bearing_materials'):
        print("警告: 没有找到支座弹塑性材料信息，无法更新支座元素")
        return

    # 遍历所有支座元素
    for i, elem_tag in enumerate(model.bearings['elements']):
        # 检查是否有该支座的材料信息
        if elem_tag not in model.params.bearing_materials:
            print(f"警告: 支座 {elem_tag} 没有对应的弹塑性材料信息")
            continue

        # 获取支座连接的节点
        deck_node, support_node = model.bearings['connections'][i]

        # 获取该支座的材料标签
        mat_info = model.params.bearing_materials[elem_tag]
        mat_tag_x = mat_info['mat_tag']

        # 重新定义支座元素
        ops.remove('element', elem_tag)
        ops.element('zeroLength',
            elem_tag, deck_node, support_node,
            '-mat',
            mat_tag_x,                   # DOF 1 (X translation) - 弹塑性材料
            mat_tag_x,                   # DOF 2 (Y translation) - 弹塑性材料
            model.mat_tags['RubberZ'],   # DOF 3 (Z translation)
            model.mat_tags['RubberRxy'], # DOF 4 (rotation about X)
            model.mat_tags['RubberRxy'], # DOF 5 (rotation about Y)
            model.mat_tags['RubberRz'],  # DOF 6 (rotation about Z)
            '-dir', 1, 2, 3, 4, 5, 6
        )

        # 输出支座信息
        # print(f"支座 {elem_tag}: 更新为弹塑性材料，屈服位移 = {mat_info['yield_disp']*1000:.3f} mm, 摩擦力 = {mat_info['friction_force']/1000:.2f} kN")
