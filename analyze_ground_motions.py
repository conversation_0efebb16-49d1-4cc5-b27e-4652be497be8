#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
地震动时程分析脚本

该脚本读取地震动加速度时程文件, 自动执行时程分析并保存桥墩x方向位移响应时程。
输入文件为numpy数组, shape为(n, T), n为地震动数量, T为时间维度。
输出为(2, n, T)的numpy数组, 保存(0,1)和(1,1)两个桥墩的位移。
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import openseespy.opensees as ops

# 导入桥梁模型和分析器
from params import BridgeParams
from core.simply_supported_beam_model import SimplySupportedBeamModel
from analysis.analyzer import BridgeAnalyzer


def run_analysis(ground_motion, dt=0.01, direction=1, pga=0.15):
    """
    执行单个地震动的分析

    参数:
        ground_motion: 地震记录数据
        dt: 时间步长
        direction: 地震方向 (1=纵向, 2=横向, 3=竖向)

    返回:
        pier_displacements: 桥墩位移数据字典 {pier_key: {'times': times, 'disp_x': disp_x}}
        analysis_stats: 分析统计信息
    """
    # 创建桥梁模型
    params = BridgeParams()
    model = SimplySupportedBeamModel(params)

    # 创建分析器
    analyzer = BridgeAnalyzer(model)
    analyzer.direction = direction

    # 运行分析
    analyzer.modal()
    analysis_stats = analyzer.dynamic(h=ground_motion, pga=pga, dt=dt)

    # 读取桥墩位移数据
    pier_displacements = {}
    
    # 检查记录器信息
    if not analyzer.recorder_info or 'piers' not in analyzer.recorder_info:
        print("警告: 没有有效的记录器信息, 无法读取桥墩位移数据")
        return pier_displacements, analysis_stats

    # 处理每个桥墩的记录数据
    for pier_id, pier_info in analyzer.recorder_info['piers'].items():
        # 获取桥墩信息
        pier_key = pier_info['pier_key']
        disp_file = pier_info['disp_file']

        # 检查文件是否存在
        if not os.path.exists(disp_file):
            print(f"警告: 文件 {disp_file} 不存在, 跳过该桥墩")
            continue

        try:
            # 读取位移数据
            data = np.loadtxt(disp_file)

            # 检查数据是否为空
            if data.size == 0:
                print(f"警告: 文件 {disp_file} 中没有数据, 跳过该桥墩")
                continue

            # 提取数据
            times = data[:, 0]
            disp_x = data[:, 1]  # X方向位移

            # 存储数据
            pier_displacements[pier_key] = {
                'times': times,
                'disp_x': disp_x
            }

        except Exception as e:
            print(f"读取文件 {disp_file} 时出错: {e}")
            continue

    return pier_displacements, analysis_stats


def main():
    """主函数"""
    dt = 0.02
    pga = 0.15
    # 设置输入输出路径
    input_file = "gm/acc_artificial_tra.npy"
    output_dir = os.path.dirname(input_file)
    output_file = input_file.replace('acc', 'dis')
    
    # 确保输入文件存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        return
    
    # 读取地震动数据
    print(f"读取地震动数据: {input_file}")
    try:
        ground_motions = np.load(input_file)
    except Exception as e:
        print(f"读取文件 {input_file} 时出错: {e}")
        return
    
    # 检查数据维度
    if ground_motions.ndim != 2:
        print(f"错误: 地震动数据维度错误: {ground_motions.shape}, 应为(n, T)")
        return
    
    n_motions, n_steps = ground_motions.shape
    print(f"地震动数量: {n_motions}, 时间步数: {n_steps}")

    # 创建输出数组 - (2, n, T) 表示2个桥墩, n个地震动, T个时间步
    # 我们需要找到(0,1)和(1,1)两个桥墩的位移
    n_calculated = 0
    target_piers = [(0, 1), (1, 1)]
    pier_displacements = np.zeros((len(target_piers), n_motions, n_steps))
    
    if os.path.exists(output_file):
        pier_displacements = np.load(output_file)
        # 检查已计算几条地震动
        n_calculated = np.count_nonzero( pier_displacements.sum((0,2)))
        print(f"已计算地震动数量: {n_calculated}")
        if n_calculated == n_motions:
            print("所有地震动均已计算, 跳过分析")
            return
        else:
            print(f"继续计算剩余地震动: {n_calculated+1} 到 {n_motions}")

    # 对每个地震动执行分析
    for i in range(n_calculated, n_motions):
        print(f"\n处理地震动 {i+1}/{n_motions}")
        
        # 获取当前地震动
        ground_motion = ground_motions[i, :]
        
        try:
            # 执行分析
            pier_disps, stats = run_analysis(ground_motion, pga=pga, dt=dt)
            
            # 检查是否找到目标桥墩
            for j, pier_key in enumerate(target_piers):
                if pier_key in pier_disps:
                    # 提取X方向位移
                    disp_x = pier_disps[pier_key]['disp_x']
                    
                    # 确保长度匹配
                    if len(disp_x) > n_steps:
                        disp_x = disp_x[:n_steps]
                    elif len(disp_x) < n_steps:
                        # 如果记录的位移数据少于时间步数, 用零填充
                        temp = np.zeros(n_steps)
                        temp[:len(disp_x)] = disp_x
                        disp_x = temp
                    
                    # 存储位移数据
                    pier_displacements[j, i, :] = disp_x
                else:
                    print(f"警告: 未找到桥墩 {pier_key}")
            
            np.save(output_file, pier_displacements)
        
        except Exception as e:
            print(f"处理地震动 {i+1} 时出错: {e}")
            import traceback
            traceback.print_exc()
    
    # 保存结果
    np.save(output_file, pier_displacements)
    print(f"\n分析完成! 结果已保存至: {output_file}")


if __name__ == "__main__":
    main()
