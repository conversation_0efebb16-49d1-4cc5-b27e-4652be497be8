"""
Deck Summarize Module

This module contains functions for summarizing and visualizing deck analysis results.
"""

from utils.deck_displacement_save import save_deck_displacements_history
from utils.deck_displacement_plot import plot_deck_displacements as plot_deck_disps


def summarize_deck_displacements(deck_disps):
    """Output a summary of deck displacement data
    
    Args:
        deck_disps: Dictionary of deck displacement data
    """
    print("\n主梁节点位移数据")
    # 检查是否有数据
    if not deck_disps:
        print("警告：没有主梁节点位移数据可供输出")
        return

    # 获取最后一个时间步
    last_time = max(deck_disps.keys())

    # 获取该时间步的数据
    step_data = deck_disps[last_time]

    # 按主梁位置排序
    sorted_data = sorted(step_data, key=lambda x: x['deck_side'])

    # 输出到文件
    output_file = 'results/deck_disps.txt'
    with open(output_file, 'w') as f:
        f.write(f"时间步: {last_time:.3f}s\n")
        f.write("=" * 80 + "\n")
        f.write("主梁位置\t节点编号\tX坐标\tY坐标\tZ坐标\t位移X(mm)\t位移Y(mm)\t位移Z(mm)\n")
        f.write("-" * 80 + "\n")

        for data in sorted_data:
            # 将位移单位从m转换为mm
            disp_x_mm = data['disp_x'] * 1000
            disp_y_mm = data['disp_y'] * 1000
            disp_z_mm = data['disp_z'] * 1000

            line = f"{data['deck_side']}\t{data['node_tag']}\t{data['x_coord']:.3f}\t{data['y_coord']:.3f}\t{data['z_coord']:.3f}\t{disp_x_mm:.3f}\t{disp_y_mm:.3f}\t{disp_z_mm:.3f}"
            f.write(line + "\n")
    print(f"- 主梁节点位移数据已保存至 {output_file}")

    # 保存历史数据到CSV文件
    csv_file = 'results/deck_disps.csv'
    save_deck_displacements_history(deck_disps, csv_file)

    # 绘制主梁节点位移时程曲线
    for direction, dir_name in [(1, 'X'), (2, 'Y'), (3, 'Z')]:
        plot_file = f'results/deck_disps_{dir_name}.png'
        plot_deck_disps(deck_disps, plot_file, direction)
