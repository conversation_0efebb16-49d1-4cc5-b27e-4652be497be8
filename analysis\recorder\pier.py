"""
Bridge Pier Response Recorder Module

This module provides functions for recording time history responses of bridge piers
during dynamic analysis using OpenSees recorders.

The module records:
- Bottom section moment
- Bottom section curvature
- Bottom section shear force
- Top node displacement
"""

import os
import openseespy.opensees as ops
import numpy as np


def find_piers_closest_to_y0(model):
    """
    Find the pier closest to y=0 at each x-coordinate.

    Args:
        model: Bridge model object

    Returns:
        dict: Dictionary mapping x-coordinate to pier key (longitudinal_idx, transverse_idx)
    """
    # Check if there are piers in the model
    if not model.piers['nodes']:
        return {}

    # Dictionary to store piers closest to y=0 at each x-coordinate
    closest_piers = {}

    # Group piers by x-coordinate
    piers_by_x = {}
    for pier_key, pier_nodes in model.piers['nodes'].items():
        # Get bottom node
        bottom_node = pier_nodes[0]

        # Get node coordinates
        x_coord = ops.nodeCoord(bottom_node, 1)
        y_coord = ops.nodeCoord(bottom_node, 2)

        # Round x-coordinate to avoid floating point issues
        x_coord_rounded = round(x_coord, 3)

        # Add to dictionary
        if x_coord_rounded not in piers_by_x:
            piers_by_x[x_coord_rounded] = []

        piers_by_x[x_coord_rounded].append((pier_key, y_coord, bottom_node))

    # Find pier closest to y=0 at each x-coordinate
    for x_coord, piers in piers_by_x.items():
        # Sort piers by absolute y-coordinate
        piers.sort(key=lambda p: abs(p[1]))

        # Get the pier closest to y=0
        closest_pier_key = piers[0][0]
        closest_piers[x_coord] = closest_pier_key

    return closest_piers


def _setup_pier_recorder(model, pier_key, dt, output_dir, recorder_info):
    """
    Set up OpenSees recorders for a single pier.

    Args:
        model: Bridge model object
        pier_key: Pier key (longitudinal_idx, transverse_idx)
        dt: Time step for recording
        output_dir: Directory to store recorder output files
        recorder_info: Dictionary to store recorder information

    Returns:
        Updated recorder_info dictionary
    """
    # Get pier nodes
    pier_nodes = model.piers['nodes'][pier_key]
    bottom_node = pier_nodes[0]
    top_node = pier_nodes[-1]

    # Get pier elements
    pier_elements = []
    for i in range(len(pier_nodes) - 1):
        n_i = pier_nodes[i]
        n_j = pier_nodes[i + 1]

        # Find element connecting these nodes
        for elem_tag in model.piers['elements']:
            elem_nodes = ops.eleNodes(elem_tag)
            if n_i in elem_nodes and n_j in elem_nodes:
                pier_elements.append(elem_tag)
                break

    # Get bottom element
    bottom_element = pier_elements[0]

    # Get pier coordinates
    pier_x = ops.nodeCoord(bottom_node, 1)
    pier_y = ops.nodeCoord(bottom_node, 2)

    # Create file names
    pier_id = f"pier_x{pier_x:.1f}_y{pier_y:.1f}"
    disp_file = f"{output_dir}/pier_disp_{pier_id}.txt"
    moment_file = f"{output_dir}/pier_moment_{pier_id}.txt"
    shear_file = f"{output_dir}/pier_shear_{pier_id}.txt"
    curvature_file = f"{output_dir}/pier_curvature_{pier_id}.txt"

    # Set up displacement recorder for top node
    disp_recorder = ops.recorder('Node', '-file', disp_file, '-time', '-dT', dt,
                                '-node', top_node, '-dof', 1, 2, 3, 'disp')

    # Set up element force recorder for bottom element
    # For forceBeamColumn elements, forces are [Vx, Vy, P, Mx, My, T]
    force_recorder = ops.recorder('Element', '-file', moment_file, '-time', '-dT', dt,
                                '-ele', bottom_element, 'localForce')

    # Set up element section recorder for bottom element
    # Record section curvature at integration point 1 (bottom)
    # For fiber sections, curvature is recorded as part of the section response
    curvature_recorder = ops.recorder('Element', '-file', curvature_file, '-time', '-dT', dt,
                                    '-ele', bottom_element, 'section', 1, 'deformation')

    # Store recorder information
    recorder_info['piers'][pier_id] = {
        'pier_key': pier_key,
        'x_coord': pier_x,
        'y_coord': pier_y,
        'bottom_node': bottom_node,
        'top_node': top_node,
        'bottom_element': bottom_element,
        'disp_file': disp_file,
        'moment_file': moment_file,
        'shear_file': shear_file,
        'curvature_file': curvature_file
    }

    recorder_info['recorder_tags'].extend([disp_recorder, force_recorder, curvature_recorder])

    return recorder_info


def setup_pier_recorders(model, dt, output_dir='results', recorder_info=None):
    """
    Set up OpenSees recorders for all piers in the model.

    Args:
        model: Bridge model object
        dt: Time step for recording
        output_dir: Directory to store recorder output files

    Returns:
        dict: Dictionary with recorder information
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Check if there are piers in the model
    if not model.piers['nodes']:
        print("警告: 模型中没有桥墩，无法设置桥墩响应记录器")
        return recorder_info if recorder_info is not None else {}

    # Initialize recorder_info if not provided
    if recorder_info is None:
        recorder_info = {
            'piers': {},
            'recorder_tags': []
        }
    else:
        # Ensure required keys exist
        if 'piers' not in recorder_info:
            recorder_info['piers'] = {}
        if 'recorder_tags' not in recorder_info:
            recorder_info['recorder_tags'] = []

    # Set up recorders for each pier
    initial_recorder_count = len(recorder_info['recorder_tags'])
    for pier_key in model.piers['nodes'].keys():
        recorder_info = _setup_pier_recorder(model, pier_key, dt, output_dir, recorder_info)

    pier_recorder_count = len(recorder_info['recorder_tags']) - initial_recorder_count
    print(f"已设置 {pier_recorder_count} 个桥墩响应记录器")
    return recorder_info

