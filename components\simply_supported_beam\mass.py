"""
Simply Supported Bridge Mass Module

This module handles the application of mass to all bridge components.
Uses lumped mass approach for dynamic analysis.
"""

import openseespy.opensees as ops
import numpy as np


def apply_mass(model):
    """
    Main method for mass matrix definition.

    Applies mass to all nodes in the bridge model using a lumped mass approach.
    The same mass value is used for all translational degrees of freedom.
    """
    # Store the mass distribution method in the model for reference
    model.mass_distribution_method = "lumped"

    # Apply mass to different components
    apply_deck_mass(model)       # Main beam/girder mass
    apply_pier_mass(model)       # Pier mass
    apply_cap_beam_mass(model)   # Cap beam mass
    apply_abutment_mass(model)   # Abutment mass
    apply_bearing_mass(model)    # Bearing mass

    # Note: OpenSees does not support consistent mass matrices
    # Rotational mass is not needed for this analysis


def apply_deck_mass(model):
    """
    Calculate and apply deck mass distribution for each span.

    Uses a lumped mass approach with equal mass in all translational directions.
    """
    # Mass factor for additional mass (pavement, barriers, etc.)
    mass_factor = 1.2

    for span_idx, span_data in model.span_nodes.items():
        span_nodes = span_data['all']
        span_length = model.params.span_lengths[span_idx]

        # Calculate section properties
        girder = model.params.girder
        section = model.params.girder_section[girder]
        width = model.params.deck_width
        height = section['height']

        # Calculate mass properties
        cross_area = width * height

        # Adjust for actual section geometry based on type
        if girder == 'box':
            # Calculate actual area considering hollow box section
            web_thickness = section['web_thickness']
            top_slab = section['top_slab_thickness']
            bottom_slab = section['bottom_slab_thickness']

            # More accurate cross-sectional area calculation
            hollow_width = width - 2 * web_thickness
            hollow_height = height - top_slab - bottom_slab
            hollow_area = hollow_width * hollow_height
            cross_area = cross_area - hollow_area

        elif girder == 'hollow_slab':
            # Calculate area for hollow slab section
            slab_width = section.get('slab_width', 0)
            slab_height = section.get('height', 0)
            hollow_width = section.get('hollow_width', 0)
            hollow_height = section.get('hollow_height', 0)

            # Calculate number of slabs
            num_slabs = int(model.params.deck_width // slab_width)

            # Calculate single slab net area
            single_slab_area = slab_width * slab_height - hollow_width * hollow_height

            # Total cross-sectional area
            cross_area = single_slab_area * num_slabs

        # Calculate mass per unit length
        line_mass = cross_area * model.params.concrete_density * mass_factor  # kg/m

        # Calculate node spacing for this span
        num_elements = len(span_nodes) - 1
        dx = span_length / num_elements  # Element length

        # Store span mass data
        model.mass_data['deck'][span_idx] = {
            'line_mass': line_mass,
            'total_mass': line_mass * span_length
        }

        # Distribute mass to nodes using lumped approach
        for i, node in enumerate(span_nodes):
            # Calculate tributary length for this node
            if i == 0 or i == len(span_nodes)-1:
                tributary_length = dx / 2  # End nodes
            else:
                tributary_length = dx  # Interior nodes

            # Calculate nodal mass
            nodal_mass = line_mass * tributary_length

            # Apply equal translational mass in all directions
            ops.mass(node,
                    nodal_mass,  # X direction (lateral)
                    nodal_mass,  # Y direction (longitudinal)
                    nodal_mass   # Z direction (vertical)
                    )


# Mass factor calculation functions have been removed
# since we're using equal mass in all translational directions


def apply_pier_mass(model):
    """
    Calculate and apply pier mass distribution.

    Uses a lumped mass approach with equal mass in all translational directions.
    """
    if not hasattr(model, 'piers') or not model.piers['nodes']:
        return  # No piers to process

    for (pier_idx_long, pier_idx_trans), pier_nodes in model.piers['nodes'].items():
        pier_height = model.params.pier_heights[pier_idx_long]
        if pier_height <= 0:
            continue

        # Calculate pier properties
        pier_diameter = model.params.pier_section['diameter']
        pier_area = np.pi * (pier_diameter/2)**2
        line_mass = pier_area * model.params.concrete_density  # kg/m

        # Store pier mass data
        model.mass_data['piers'][(pier_idx_long, pier_idx_trans)] = {
            'line_mass': line_mass,
            'total_mass': line_mass * pier_height
        }

        # Calculate node spacing
        num_elements = len(pier_nodes) - 1
        dz = pier_height / num_elements

        # Distribute mass to nodes
        for i, node in enumerate(pier_nodes):
            # Calculate tributary length for this node
            if i == 0 or i == len(pier_nodes)-1:
                tributary_length = dz / 2  # End nodes
            else:
                tributary_length = dz  # Interior nodes

            # Calculate nodal mass
            nodal_mass = line_mass * tributary_length

            # Apply equal translational mass in all directions
            ops.mass(node,
                    nodal_mass,  # X direction
                    nodal_mass,  # Y direction
                    nodal_mass   # Z direction
                    )


def apply_cap_beam_mass(model):
    """
    Calculate and apply cap beam mass.

    Uses a lumped mass approach with equal mass in all translational directions.
    """
    if not hasattr(model, 'cap_beams') or not model.cap_beams['nodes']:
        return  # No cap beams to process

    for cap_idx, cap_nodes in model.cap_beams['nodes'].items():
        # Calculate actual cap beam length based on node positions
        if len(cap_nodes) > 1:
            first_node = cap_nodes[0]
            last_node = cap_nodes[-1]
            first_y = ops.nodeCoord(first_node, 2)
            last_y = ops.nodeCoord(last_node, 2)
            cap_beam_length = abs(last_y - first_y)
        else:
            # Default length if only one node
            cap_beam_length = model.params.cap_beam_section['length']

        # Get cap beam cross-section properties
        cap_beam_width = model.params.cap_beam_section['width']
        cap_beam_height = model.params.cap_beam_section['height']

        # Calculate volume and mass
        cap_volume = cap_beam_width * cap_beam_height * cap_beam_length
        cap_mass = cap_volume * model.params.concrete_density

        # Store cap beam mass data
        model.mass_data['cap_beams'][cap_idx] = {
            'total_mass': cap_mass
        }

        # Calculate node spacing for mass distribution
        if len(cap_nodes) > 1:
            # Calculate node spacings
            node_spacings = []
            for i in range(len(cap_nodes)-1):
                node_i = cap_nodes[i]
                node_j = cap_nodes[i+1]
                y_i = ops.nodeCoord(node_i, 2)
                y_j = ops.nodeCoord(node_j, 2)
                node_spacings.append(abs(y_j - y_i))
            avg_spacing = sum(node_spacings) / len(node_spacings)

            # Distribute mass to nodes based on tributary length
            for i, node in enumerate(cap_nodes):
                # Calculate tributary length
                if i == 0 or i == len(cap_nodes)-1:
                    # End nodes get half the tributary length
                    tributary_ratio = (avg_spacing / 2) / cap_beam_length
                else:
                    # Interior nodes get full tributary length
                    tributary_ratio = avg_spacing / cap_beam_length

                # Calculate nodal mass
                nodal_mass = cap_mass * tributary_ratio

                # Apply equal translational mass in all directions
                ops.mass(node,
                        nodal_mass,  # X direction
                        nodal_mass,  # Y direction
                        nodal_mass   # Z direction
                        )
        else:
            # Only one node - assign all mass to it
            ops.mass(cap_nodes[0],
                    cap_mass,  # X direction
                    cap_mass,  # Y direction
                    cap_mass   # Z direction
                    )


def apply_abutment_mass(model):
    """
    Calculate and apply mass to abutment nodes.

    Uses a lumped mass approach with equal mass in all translational directions.
    """
    if not hasattr(model, 'abutments') or not model.abutments['nodes']:
        return  # No abutment nodes to process

    # Initialize abutment mass data in model if not already present
    if 'abutments' not in model.mass_data:
        model.mass_data['abutments'] = {}

    # Estimate abutment mass based on typical dimensions
    # This is a simplified approach - in a real bridge, abutment mass would be calculated
    # based on actual dimensions and material properties

    # Typical abutment dimensions
    abutment_height = 5.0  # m (typical height)
    abutment_width = model.params.deck_width  # m (same as deck width)
    abutment_thickness = 1.5  # m (typical thickness)

    # Calculate volume and mass
    abutment_volume = abutment_height * abutment_width * abutment_thickness * 2
    abutment_mass = abutment_volume * model.params.concrete_density  # kg

    # Store abutment mass data
    model.mass_data['abutments']['total_mass'] = abutment_mass

    # Apply mass to each abutment node
    for i, node in enumerate(model.abutments['nodes']):
        # Divide mass equally among abutment nodes
        nodal_mass = abutment_mass / len(model.abutments['nodes'])

        # Apply equal translational mass in all directions
        ops.mass(node,
                nodal_mass,  # X direction
                nodal_mass,  # Y direction
                nodal_mass   # Z direction
                )


def apply_bearing_mass(model):
    """
    Calculate and apply mass to bearing nodes.
    
    Uses a lumped mass approach with equal mass in all translational directions.
    """
    if not hasattr(model, 'bearings') or not model.bearings['connections']:
        return  # No bearing nodes to process
    
    # Initialize bearing mass data in model if not already present
    if 'bearings' not in model.mass_data:
        model.mass_data['bearings'] = {}
    
    # Get bearing properties
    bearing_type = model.params.rubber
    bearing_props = model.params.bearing[bearing_type]
    
    # Calculate bearing mass based on dimensions
    bearing_volume = bearing_props['A'] * bearing_props['t']  # m³
    bearing_mass = bearing_volume * model.params.steel_density * 0.5  # kg (50% steel, 50% rubber)
    
    # Store bearing mass data
    model.mass_data['bearings']['unit_mass'] = bearing_mass
    model.mass_data['bearings']['total_mass'] = bearing_mass * len(model.bearings['connections'])
    
    # Apply mass to each bearing node
    for deck_node, support_node in model.bearings['connections']:
        # Apply mass to support node (connection to cap beam or abutment)
        ops.mass(deck_node,
                bearing_mass,  # X direction
                bearing_mass,  # Y direction
                bearing_mass   # Z direction
                )


# Note: Rotational mass is not needed for this analysis and is commented out
# def apply_rotational_mass(model):
#     """Apply rotational mass to nodes for more accurate dynamic behavior"""
#     # This function is not used in the current implementation
#     pass

