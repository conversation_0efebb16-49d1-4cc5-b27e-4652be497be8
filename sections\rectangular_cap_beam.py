import openseespy.opensees as ops
import numpy as np


def set_rectangular_cap_beam_section(section_tag, params, mat_tags):
    """创建矩形盖梁纤维截面

    盖梁采用矩形截面，包含核心区约束混凝土、保护层非约束混凝土和纵向钢筋
    """
    # 获取截面参数
    width = params.cap_beam_section['width']    # 盖梁宽度
    height = params.cap_beam_section['height']  # 盖梁高度

    # 盖梁保护层厚度
    cover = params.cap_beam_section['concrete_cover']

    # 计算核心区尺寸
    core_width = width - 2 * cover
    core_height = height - 2 * cover

    # 计算扭转刚度GJ
    G = params.concrete_materials['core']['Ec'] / 2.4  # 剪切模量
    J = width * height**3 * (1/3 - 0.21 * (height/width) * (1 - (height/width)**4/12))  # 矩形截面扭转惯性矩近似公式
    GJ = G * J

    # 创建纤维截面
    ops.section('Fiber', section_tag, '-GJ', GJ)

    # 创建保护层混凝土（非约束混凝土）
    create_cover_concrete(width, height, core_width, core_height, mat_tags['ConcreteCover'])

    # 创建核心区混凝土（约束混凝土）
    create_core_concrete(core_width, core_height, mat_tags['ConcreteCore'])

    # 创建纵向钢筋
    create_longitudinal_reinforcement(width, height, cover, mat_tags['SteelLongitudinal'], params)

    return section_tag


def create_cover_concrete(width, height, core_width, core_height, mat_tag):
    """创建保护层混凝土纤维（非约束混凝土）"""
    # 计算保护层厚度
    cover_thickness = (width - core_width) / 2

    # 顶部保护层
    ops.patch('quad', mat_tag, 10, 2,
            0.0, core_height,           # 左下角
            width, core_height,         # 右下角
            width, height,              # 右上角
            0.0, height)                # 左上角

    # 底部保护层
    ops.patch('quad', mat_tag, 10, 2,
            0.0, 0.0,                   # 左下角
            width, 0.0,                 # 右下角
            width, cover_thickness,     # 右上角
            0.0, cover_thickness)       # 左上角

    # 左侧保护层
    ops.patch('quad', mat_tag, 2, 10,
            0.0, cover_thickness,       # 左下角
            cover_thickness, cover_thickness, # 右下角
            cover_thickness, core_height, # 右上角
            0.0, core_height)           # 左上角

    # 右侧保护层
    ops.patch('quad', mat_tag, 2, 10,
            width - cover_thickness, cover_thickness, # 左下角
            width, cover_thickness,     # 右下角
            width, core_height,         # 右上角
            width - cover_thickness, core_height) # 左上角


def create_core_concrete(core_width, core_height, mat_tag):
    """创建核心区混凝土纤维（约束混凝土）"""
    # 核心区混凝土（矩形区域）
    ops.patch('quad', mat_tag, 10, 10,
            0.0, 0.0,                   # 左下角
            core_width, 0.0,            # 右下角
            core_width, core_height,    # 右上角
            0.0, core_height)           # 左上角


def create_longitudinal_reinforcement(width, height, cover, mat_tag, params=None):
    """创建纵向钢筋"""
    # 从参数中获取钢筋直径和间距
    if params and 'rebar_dia' in params.cap_beam_section:
        bar_dia = params.cap_beam_section['rebar_dia']
    else:
        bar_dia = 0.020  # 默认值 20mm

    bar_area = np.pi * (bar_dia/2)**2

    # 钢筋间距
    if params and 'spacing' in params.cap_beam_section:
        spacing = params.cap_beam_section['spacing']
    else:
        spacing = 0.2  # 默认值 200mm

    # 计算底部和顶部钢筋数量
    num_bottom_bars = max(3, int(width / spacing))
    num_top_bars = max(3, int(width / spacing))

    # 计算左右侧钢筋数量（不包括顶部和底部的角部钢筋）
    num_side_bars = max(2, int((height - 2*cover) / spacing) - 2)

    # 底部钢筋
    ops.layer('straight', mat_tag,
            num_bottom_bars,
            bar_area,
            cover, cover,                # 起点
            width - cover, cover)        # 终点

    # 顶部钢筋
    ops.layer('straight', mat_tag,
            num_top_bars,
            bar_area,
            cover, height - cover,       # 起点
            width - cover, height - cover) # 终点

    # 左侧钢筋（不包括顶部和底部的角部钢筋）
    if num_side_bars > 0:
        side_spacing = (height - 2*cover) / (num_side_bars + 1)
        for i in range(num_side_bars):
            y_pos = cover + (i + 1) * side_spacing
            ops.layer('straight', mat_tag,
                    1,
                    bar_area,
                    cover, y_pos,         # 起点
                    cover, y_pos)         # 终点

    # 右侧钢筋（不包括顶部和底部的角部钢筋）
    if num_side_bars > 0:
        side_spacing = (height - 2*cover) / (num_side_bars + 1)
        for i in range(num_side_bars):
            y_pos = cover + (i + 1) * side_spacing
            ops.layer('straight', mat_tag,
                    1,
                    bar_area,
                    width - cover, y_pos, # 起点
                    width - cover, y_pos) # 终点
