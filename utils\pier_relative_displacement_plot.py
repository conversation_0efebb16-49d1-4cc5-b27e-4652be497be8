"""
桥墩相对位移绘图模块

该模块提供函数用于绘制桥墩相对位移随时间的变化曲线。
"""

import matplotlib.pyplot as plt


def plot_pier_relative_displacements(pier_relative_disps, output_file="pier_relative_disps.png", direction=1):
    """绘制桥墩相对位移随时间的变化

    参数:
        pier_relative_disps: 桥墩相对位移数据字典 {time: [pier_data, ...]}
        output_file: 输出文件路径
        direction: 位移方向 1=X, 2=Y, 3=Z
    """
    # 检查是否有数据
    if not pier_relative_disps:
        print("警告：没有桥墩相对位移数据可供绘制")
        return

    # 确定方向标签
    dir_labels = {1: 'X', 2: 'Y', 3: 'Z'}
    dir_label = dir_labels.get(direction, 'X')

    # 准备数据
    times = sorted(pier_relative_disps.keys())

    # 获取所有桥墩的唯一标识
    pier_ids = set()
    for time in times:
        for data in pier_relative_disps[time]:
            pier_ids.add((data['pier_long_idx'], data['pier_trans_idx']))

    # 创建图形
    plt.figure(figsize=(12, 8), dpi=150)
    plt.title(f'relative displacement in {dir_label} direction', fontsize=14, fontweight='bold')
    plt.xlabel('time (s)', fontsize=12)
    plt.ylabel(f'relative displacement (direction {dir_label}) (mm)', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.7)

    # 为每个桥墩绘制时程曲线
    for pier_id in sorted(pier_ids):
        pier_long_idx, pier_trans_idx = pier_id

        # 提取该桥墩的位移数据
        disp_data = []
        for time in times:
            for data in pier_relative_disps[time]:
                if (data['pier_long_idx'], data['pier_trans_idx']) == pier_id:
                    if direction == 1:
                        disp_data.append(data['rel_disp_x'] * 1000)  # 转换为mm
                    elif direction == 2:
                        disp_data.append(data['rel_disp_y'] * 1000)  # 转换为mm
                    elif direction == 3:
                        disp_data.append(data['rel_disp_z'] * 1000)  # 转换为mm
                    break

        # 绘制曲线
        label = f'pier ({pier_long_idx},{pier_trans_idx})'
        plt.plot(times, disp_data, label=label, linewidth=1.5)

    # 添加图例 - 将图例放在图形下方
    # 计算合适的列数 - 根据桥墩数量动态调整
    num_piers = len(pier_ids)
    if num_piers <= 4:
        ncols = num_piers
    elif num_piers <= 8:
        ncols = 4
    elif num_piers <= 15:
        ncols = 5
    else:
        ncols = 6

    # 使用bbox_to_anchor将图例放在图形下方
    plt.legend(fontsize='small',
               loc='upper center',
               bbox_to_anchor=(0.5, -0.15),
               ncol=ncols)

    # 保存图形 - 增加底部边距以容纳图例
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.2)  # 增加底部边距
    plt.savefig(output_file, bbox_inches='tight')  # 使用bbox_inches确保图例完全显示
    plt.close()

    print(f"- 桥墩相对位移时程曲线已保存至 {output_file}")
