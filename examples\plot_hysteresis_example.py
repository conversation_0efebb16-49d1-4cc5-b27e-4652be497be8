"""
地震时程分析滞回曲线绘制示例

该脚本演示如何使用 HysteresisPlotter 模块绘制：
1. 第一个支座的力-位移滞回曲线
2. 第一个桥墩底部的弯矩-曲率滞回曲线

使用前请确保已完成地震时程分析，并生成了相应的结果文件。

作者: <PERSON><PERSON><PERSON> Xu
日期: 2025-08-16
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.hysteresis_plotter import HysteresisPlotter, plot_seismic_hysteresis
from params import BridgeParams
from core.simply_supported_beam_model import SimplySupportedBeamModel


def main():
    """主函数：演示滞回曲线绘制"""
    
    print("=" * 60)
    print("地震时程分析滞回曲线绘制示例")
    print("=" * 60)
    
    # 1. 创建桥梁模型（用于获取支座参数）
    print("\n1. 创建桥梁模型...")
    params = BridgeParams()
    model = SimplySupportedBeamModel(params)
    print(f"   桥梁跨数: {params.num_spans}")
    print(f"   桥墩数量: {len(model.piers['nodes'])}")
    print(f"   支座数量: {len(model.bearings['elements'])}")
    
    # 2. 设置结果目录
    results_dir = 'results'
    if not os.path.exists(results_dir):
        print(f"\n警告: 结果目录 {results_dir} 不存在")
        print("请先运行地震时程分析生成结果文件")
        return
    
    # 3. 创建滞回曲线绘制器
    print("\n2. 创建滞回曲线绘制器...")
    plotter = HysteresisPlotter(results_dir)
    
    # 4. 加载支座数据
    print("\n3. 加载支座相对位移数据...")
    bearing_data = plotter.load_bearing_data(model=model)
    
    if not bearing_data:
        print("   未找到支座数据，跳过支座滞回曲线绘制")
    else:
        print(f"   成功加载 {len(bearing_data)} 个时间步的支座数据")
        
        # 绘制第一个支座的滞回曲线
        print("\n4. 绘制支座滞回曲线...")
        bearing_idx = 0
        save_path = os.path.join(results_dir, f'bearing_{bearing_idx}_hysteresis.png')
        
        # 测试X方向（带符号）
        try:
            save_path_x = os.path.join(results_dir, f'bearing_{bearing_idx}_x_hysteresis.png')
            plotter.plot_bearing_hysteresis(
                bearing_idx=bearing_idx,
                direction='x',
                save_path=save_path_x,
                show_plot=True
            )
            print(f"   支座 #{bearing_idx} X方向滞回曲线绘制完成")
        except Exception as e:
            print(f"   绘制支座X方向滞回曲线时出错: {e}")

        # # 测试Y方向（带符号）
        # try:
        #     save_path_y = os.path.join(results_dir, f'bearing_{bearing_idx}_y_hysteresis.png')
        #     plotter.plot_bearing_hysteresis(
        #         bearing_idx=bearing_idx,
        #         direction='y',
        #         save_path=save_path_y,
        #         show_plot=True
        #     )
        #     print(f"   支座 #{bearing_idx} Y方向滞回曲线绘制完成")
        # except Exception as e:
        #     print(f"   绘制支座Y方向滞回曲线时出错: {e}")

        # # 测试水平合成（正值）
        # try:
        #     save_path_h = os.path.join(results_dir, f'bearing_{bearing_idx}_h_hysteresis.png')
        #     plotter.plot_bearing_hysteresis(
        #         bearing_idx=bearing_idx,
        #         direction='h',
        #         save_path=save_path_h,
        #         show_plot=True  # 只显示最后一个
        #     )
        #     print(f"   支座 #{bearing_idx} 水平合成滞回曲线绘制完成")
        # except Exception as e:
        #     print(f"   绘制支座水平合成滞回曲线时出错: {e}")
    
    # 5. 查找可用的桥墩数据文件
    print("\n5. 查找桥墩响应数据文件...")
    pier_files = []
    for file in os.listdir(results_dir):
        if file.startswith('pier_disp_') and file.endswith('.txt'):
            pier_id = file.replace('pier_disp_', '').replace('.txt', '')
            pier_files.append(pier_id)
    
    if not pier_files:
        print("   未找到桥墩数据文件，跳过桥墩滞回曲线绘制")
    else:
        print(f"   找到 {len(pier_files)} 个桥墩数据文件")
        
        # 选择第一个桥墩进行分析
        pier_id = pier_files[0]
        print(f"   选择桥墩: {pier_id}")
        
        # 6. 加载桥墩数据
        print("\n6. 加载桥墩响应数据...")
        pier_data = plotter.load_pier_data(pier_id)
        
        if not pier_data:
            print("   桥墩数据加载失败")
        else:
            print(f"   成功加载桥墩 {pier_id} 的响应数据")
            
            # 绘制桥墩弯矩-曲率滞回曲线
            print("\n7. 绘制桥墩弯矩-曲率滞回曲线...")
            
            for direction in ['y']: # ['x', 'y']:
                save_path = os.path.join(results_dir, f'pier_{pier_id}_{direction}_hysteresis.png')
                
                try:
                    plotter.plot_pier_hysteresis(
                        pier_id=pier_id,
                        direction=direction,
                        save_path=save_path,
                        show_plot=True
                    )
                    print(f"   桥墩 {pier_id} {direction.upper()}方向滞回曲线绘制完成")
                except Exception as e:
                    print(f"   绘制桥墩 {direction.upper()}方向滞回曲线时出错: {e}")
    
    # 8. 绘制综合滞回曲线图
    print("\n8. 绘制综合滞回曲线图...")
    try:
        if bearing_data and pier_files:
            plotter.plot_comprehensive_hysteresis(
                bearing_idx=bearing_idx,
                pier_id=pier_files[0] if pier_files else None,
                save_dir=results_dir
            )
            print("   综合滞回曲线图绘制完成")
        else:
            print("   数据不完整，跳过综合图绘制")
    except Exception as e:
        print(f"   绘制综合滞回曲线图时出错: {e}")
    
    print("\n" + "=" * 60)
    print("滞回曲线绘制完成！")
    print(f"结果文件保存在: {os.path.abspath(results_dir)}")
    print("=" * 60)


def quick_plot_example():
    """快速绘制示例：使用便捷函数"""
    
    print("\n" + "=" * 60)
    print("快速绘制示例")
    print("=" * 60)
    
    # 创建桥梁模型
    params = BridgeParams()
    model = SimplySupportedBeamModel(params)
    
    # 查找第一个桥墩
    results_dir = 'results'
    bearing_idx = 20
    pier_id = None
    
    if os.path.exists(results_dir):
        for file in os.listdir(results_dir):
            if file.startswith('pier_disp_') and file.endswith('.txt'):
                pier_id = file.replace('pier_disp_', '').replace('.txt', '')
                break
    
    # 使用便捷函数绘制
    try:
        plot_seismic_hysteresis(
            results_dir=results_dir,
            bearing_idx=bearing_idx,
            pier_id=pier_id,
            model=model
        )
        print("快速绘制完成！")
    except Exception as e:
        print(f"快速绘制时出错: {e}")


if __name__ == "__main__":
    # 运行主示例
    main()
    
    # 运行快速绘制示例
    # quick_plot_example()
