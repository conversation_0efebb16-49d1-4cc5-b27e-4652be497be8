import openseespy.opensees as ops
import numpy as np


def set_circular_pier_section(section_tag, params, mat_tags):
    """创建圆形桥墩纤维截面,
    包含核心区约束混凝土、保护层非约束混凝土、纵向钢筋和横向箍筋
    """
    # 获取截面参数
    diameter = params.pier_section['diameter']
    cover = params.pier_section['concrete_cover']

    # 钢筋参数
    long_bars = params.pier_section['longitudinal_bars']
    long_bar_dia = long_bars['diameter']
    num_long_bars = long_bars['number']
    bar_area = np.pi * (long_bar_dia/2)**2

    # 计算核心区直径
    core_dia = diameter - 2 * cover

    # 计算扭转刚度GJ
    G_concrete = params.concrete_materials['core']['Ec'] / 2.4
    G_steel = params.steel_materials['longitudinal']['Es'] / 2.6  # 剪切模量
    
    # 混凝土部分的扭转惯性矩
    J_concrete = np.pi * diameter**4 / 32  # 实心圆截面的抗扭惯性矩
    
    # 钢筋对扭转刚度的贡献
    # 计算单根钢筋的极惯性矩
    J_single_bar = np.pi * (long_bar_dia/2)**4 / 2
    
    # 计算所有钢筋的极惯性矩总和（考虑平行轴定理）
    rebar_radius = core_dia/2 - long_bar_dia/2
    J_steel = num_long_bars * (J_single_bar + np.pi * (long_bar_dia/2)**2 * rebar_radius**2)
    
    # 计算总扭转刚度
    GJ = G_concrete * J_concrete + G_steel * J_steel

    # 创建纤维截面
    ops.section(
        'RCCircularSection', section_tag, 
        mat_tags['ConcreteCore'], mat_tags['ConcreteCover'], mat_tags['SteelLongitudinal'], 
        diameter/2, cover, bar_area, 32, 32, 32, num_long_bars, '-GJ', GJ
    )

    # ops.section('Fiber', section_tag, '-GJ', GJ)

    # # 创建保护层混凝土（非约束混凝土）
    # create_cover_concrete(diameter, core_dia, mat_tags['ConcreteCover'])

    # # 创建核心区混凝土（约束混凝土）
    # create_core_concrete(core_dia, mat_tags['ConcreteCore'])

    # # 创建纵向钢筋
    # create_longitudinal_reinforcement(core_dia, num_long_bars, long_bar_dia, mat_tags['SteelLongitudinal'])

    return section_tag


def create_cover_concrete(diameter, core_dia, mat_tag):
    """创建保护层混凝土纤维（非约束混凝土）"""
    # 环向分段数（越多越精确，但计算量增加）
    num_subdiv_circ = 36

    # 径向分段数（保护层只需要1层）
    num_subdiv_rad = 1

    # 创建外环保护层混凝土（环形区域）
    ops.patch('circ', mat_tag,          # 材料标签（非约束混凝土）
            num_subdiv_circ,            # 环向分段数
            num_subdiv_rad,             # 径向分段数
            0.0, 0.0,                   # 圆心坐标
            core_dia/2,                 # 内径
            diameter/2,                 # 外径
            0.0, 360.0)                 # 起止角度（整圆）


def create_core_concrete(core_dia, mat_tag):
    """创建核心区混凝土纤维（约束混凝土）"""
    # 环向分段数
    num_subdiv_circ = 36

    # 径向分段数（核心区可以分多层以提高精度）
    num_subdiv_rad = 8

    # 创建核心区混凝土（实心圆）
    ops.patch('circ', mat_tag,          # 材料标签（约束混凝土）
            num_subdiv_circ,            # 环向分段数
            num_subdiv_rad,             # 径向分段数
            0.0, 0.0,                   # 圆心坐标
            0.0,                        # 内径（实心圆从0开始）
            core_dia/2,                 # 外径
            0.0, 360.0)                 # 起止角度（整圆）


def create_longitudinal_reinforcement(core_dia, num_bars, bar_dia, mat_tag):
    """创建纵向钢筋"""
    # 计算单根钢筋面积
    bar_area = np.pi * (bar_dia/2)**2

    # 钢筋布置半径（位于核心区边缘内侧）
    rebar_radius = core_dia/2 - bar_dia/2

    # 创建环形分布的纵向钢筋
    ops.layer('circ', mat_tag,          # 材料标签（纵向钢筋）
            num_bars,                   # 钢筋数量
            bar_area,                   # 单根钢筋面积
            0.0, 0.0,                   # 圆心坐标
            rebar_radius,               # 钢筋布置半径
            0.0, 360.0)                 # 起止角度（整圆）