"""
主梁节点位移保存模块

该模块提供函数用于保存主梁节点位移历史数据到CSV文件。
"""


def save_deck_displacements_history(deck_disps, output_file):
    """保存所有时间步的主梁节点位移历史数据到CSV文件

    参数:
        deck_disps: 主梁节点位移数据字典 {time: [deck_data, ...]}
        output_file: 输出文件路径
    """
    # 检查是否有数据
    if not deck_disps:
        print("警告：没有主梁节点位移数据可供保存")
        return

    # 打开文件
    with open(output_file, 'w') as f:
        # 写入表头
        f.write("时间(s),主梁位置,节点编号,X坐标,Y坐标,Z坐标,位移X(mm),位移Y(mm),位移Z(mm)\n")

        # 按时间排序
        for time in sorted(deck_disps.keys()):
            # 获取该时间步的数据
            step_data = deck_disps[time]

            # 按主梁位置排序
            sorted_data = sorted(step_data, key=lambda x: (x['deck_side'], x['node_tag']))

            # 写入数据
            for data in sorted_data:
                # 将位移单位从m转换为mm
                disp_x_mm = data['disp_x'] * 1000
                disp_y_mm = data['disp_y'] * 1000
                disp_z_mm = data['disp_z'] * 1000

                line = f"{time:.3f},{data['deck_side']},{data['node_tag']},{data['x_coord']:.3f},{data['y_coord']:.3f},{data['z_coord']:.3f},{disp_x_mm:.3f},{disp_y_mm:.3f},{disp_z_mm:.3f}"
                f.write(line + '\n')

    print(f"- 主梁节点位移历史数据已保存至 {output_file}")
