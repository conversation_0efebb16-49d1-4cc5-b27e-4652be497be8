import os
import json

def recursive_update(obj_attr, value):
    """
    递归更新:
    - 如果 obj_attr 和 value 都是 dict, 则只更新交集或新增键, 不覆盖整个 dict
    - 否则直接替换
    """
    if isinstance(obj_attr, dict) and isinstance(value, dict):
        for k, v in value.items():
            if k in obj_attr:
                obj_attr[k] = recursive_update(obj_attr[k], v)
            else:
                obj_attr[k] = v
        return obj_attr
    else:
        # 基础类型或列表等，直接覆盖
        return value
    
    
def update_from_config(file_path: str, bridge_params):
    """
    从 JSON 配置文件读取并更新到 BridgeParams 实例
    ---------------------------------
    根据配置字典更新 BridgeParams 实例：
    - 如果属性在类中存在：
        * dict -> 递归更新
        * 非 dict -> 覆盖
    - 如果属性不存在 -> 新增
    """
    if file_path is None:
        return bridge_params
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"配置文件 {file_path} 不存在")

    with open(file_path, "r", encoding="utf-8") as f:
        config = json.load(f)
        
    for key, value in config.items():
        if hasattr(bridge_params, key):
            current_val = getattr(bridge_params, key)
            setattr(bridge_params, key, recursive_update(current_val, value))
        else:
            setattr(bridge_params, key, value)
    return bridge_params

