import openseespy.opensees as ops
import numpy as np


def set_hollow_slab_section(section_tag, params):
        """创建混凝土空心板梁纤维截面
        并排的空心板梁数量由deck_width//1.0确定
        """
        section_params = params.girder_section["hollow_slab"]
        
        # 单个空心板梁参数
        slab_width = section_params["slab_width"]       # 单个空心板宽度 (m)
        slab_height = section_params["height"]          # 单个空心板高度 (m)
        hollow_width = section_params["hollow_width"]   # 空洞宽度 (m)
        hollow_height = section_params["hollow_height"] # 空洞高度 (m)
        cover = section_params.get("cover", 0.05)       # 保护层厚度 (m)
        
        # 计算并排空心板梁数量
        num_slabs = int(params.deck_width // slab_width)
        
        # 计算总截面宽度
        total_width = num_slabs * slab_width
        
        # 计算扭转刚度GJ
        G = params.concrete_materials['core']['Ec'] / 2.4  # 剪切模量
        
        # 计算总截面的扭转惯性矩
        # 对于每个空心板，计算其扭转惯性矩
        J_single_slab = calculate_hollow_rect_torsion_J(
                slab_width, slab_height, hollow_width, hollow_height)
        
        # 总扭转惯性矩为所有空心板的和
        J_total = J_single_slab * num_slabs
        GJ = G * J_total
        
        # 创建纤维截面并指定扭转刚度
        ops.section('Fiber', section_tag, '-GJ', GJ)
        
        # 为每个空心板创建纤维截面
        for i in range(num_slabs):
                # 计算当前空心板的中心位置
                x_offset = i * slab_width - total_width/2 + slab_width/2
                
                # 创建顶板混凝土
                top_thickness = (slab_height - hollow_height) / 2
                ops.patch('quad', 1, 5, 2,
                        x_offset - slab_width/2, slab_height/2 - top_thickness,  # 左下角
                        x_offset + slab_width/2, slab_height/2 - top_thickness,  # 右下角
                        x_offset + slab_width/2, slab_height/2,                  # 右上角
                        x_offset - slab_width/2, slab_height/2)                  # 左上角
                
                # 创建底板混凝土
                bottom_thickness = (slab_height - hollow_height) / 2
                ops.patch('quad', 1, 5, 2,
                        x_offset - slab_width/2, -slab_height/2,                  # 左下角
                        x_offset + slab_width/2, -slab_height/2,                  # 右下角
                        x_offset + slab_width/2, -slab_height/2 + bottom_thickness, # 右上角
                        x_offset - slab_width/2, -slab_height/2 + bottom_thickness) # 左上角
                
                # 创建左侧腹板混凝土
                web_thickness = (slab_width - hollow_width) / 2
                ops.patch('quad', 1, 2, 5,
                        x_offset - slab_width/2, -slab_height/2 + bottom_thickness,  # 左下角
                        x_offset - slab_width/2 + web_thickness, -slab_height/2 + bottom_thickness,  # 右下角
                        x_offset - slab_width/2 + web_thickness, slab_height/2 - top_thickness,  # 右上角
                        x_offset - slab_width/2, slab_height/2 - top_thickness)  # 左上角
                
                # 创建右侧腹板混凝土
                ops.patch('quad', 1, 2, 5,
                        x_offset + slab_width/2 - web_thickness, -slab_height/2 + bottom_thickness,  # 左下角
                        x_offset + slab_width/2, -slab_height/2 + bottom_thickness,  # 右下角
                        x_offset + slab_width/2, slab_height/2 - top_thickness,  # 右上角
                        x_offset + slab_width/2 - web_thickness, slab_height/2 - top_thickness)  # 左上角
                
                # 添加钢筋（简化处理，仅在顶板和底板添加）
                # 底板钢筋
                ops.layer('straight', 2,  # 钢筋材料标签
                        3,  # 钢筋数量
                        0.0004,  # 钢筋面积 (m²)
                        x_offset - slab_width/2 + cover, -slab_height/2 + cover,  # 起点
                        x_offset + slab_width/2 - cover, -slab_height/2 + cover)  # 终点
                
                # 顶板钢筋
                ops.layer('straight', 2,  # 钢筋材料标签
                        3,  # 钢筋数量
                        0.0004,  # 钢筋面积 (m²)
                        x_offset - slab_width/2 + cover, slab_height/2 - cover,  # 起点
                        x_offset + slab_width/2 - cover, slab_height/2 - cover)  # 终点
        
        return section_tag


def calculate_hollow_rect_torsion_J(outer_width, outer_height, inner_width, inner_height):
        """计算空心矩形截面的扭转惯性矩J"""
        # 外部矩形的扭转惯性矩
        J_outer = (outer_width * outer_height**3 + outer_height * outer_width**3) / 12
        
        # 内部空洞的扭转惯性矩
        J_inner = (inner_width * inner_height**3 + inner_height * inner_width**3) / 12
        
        # 空心矩形的扭转惯性矩 = 外部 - 内部
        J = J_outer - J_inner
        
        return J
