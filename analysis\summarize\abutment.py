"""
Abutment Summarize Module

This module contains functions for summarizing and visualizing abutment analysis results.
"""

from utils.abutment_displacement_save import save_abutment_displacements_history
from utils.abutment_displacement_plot import plot_abutment_displacements as plot_abutment_disps


def summarize_abutment_displacements(abutment_disps):
    """Output a summary of abutment displacement data
    
    Args:
        abutment_disps: Dictionary of abutment displacement data
    """
    print("\n桥台节点位移数据")
    # 检查是否有数据
    if not abutment_disps:
        print("警告：没有桥台节点位移数据可供输出")
        return

    # 获取最后一个时间步
    last_time = max(abutment_disps.keys())

    # 获取该时间步的数据
    step_data = abutment_disps[last_time]

    # 按桥台位置排序
    sorted_data = sorted(step_data, key=lambda x: x['abutment_side'])

    # 输出到文件
    output_file = 'results/abutment_disps.txt'
    with open(output_file, 'w') as f:
        f.write(f"时间步: {last_time:.3f}s\n")
        f.write("=" * 80 + "\n")
        f.write("桥台位置\t节点编号\tX坐标\tY坐标\tZ坐标\t位移X(mm)\t位移Y(mm)\t位移Z(mm)\n")
        f.write("-" * 80 + "\n")

        for data in sorted_data:
            # 将位移单位从m转换为mm
            disp_x_mm = data['disp_x'] * 1000
            disp_y_mm = data['disp_y'] * 1000
            disp_z_mm = data['disp_z'] * 1000

            line = f"{data['abutment_side']}\t{data['node_tag']}\t{data['x_coord']:.3f}\t{data['y_coord']:.3f}\t{data['z_coord']:.3f}\t{disp_x_mm:.3f}\t{disp_y_mm:.3f}\t{disp_z_mm:.3f}"
            f.write(line + "\n")
    print(f"- 桥台节点位移数据已保存至 {output_file}")

    # 保存历史数据到CSV文件
    csv_file = 'results/abutment_disps.csv'
    save_abutment_displacements_history(abutment_disps, csv_file)

    # 绘制桥台节点位移时程曲线
    for direction, dir_name in [(1, 'X'), (2, 'Y'), (3, 'Z')]:
        plot_file = f'results/abutment_disps_{dir_name}.png'
        plot_abutment_disps(abutment_disps, plot_file, direction)
