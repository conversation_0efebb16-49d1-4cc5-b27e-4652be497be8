"""
桥台节点位移输出模块

该模块提供函数用于输出桥台节点位移数据到控制台或文件。
"""


def output_abutment_displacements(abutment_disps, output_file=None, time_step=None):
    """输出桥台节点位移数据

    参数:
        abutment_disps: 桥台节点位移数据字典 {time: [abutment_data, ...]}
        output_file: 输出文件路径, 如果为None则输出到控制台
        time_step: 指定输出的时间步, 如果为None则输出最后一个时间步
    """
    # 检查是否有数据
    if not abutment_disps:
        print("警告：没有桥台节点位移数据可供输出")
        return

    # 确定要输出的时间步
    if time_step is None:
        # 获取最后一个时间步
        time_step = max(abutment_disps.keys())

    # 检查指定的时间步是否存在
    if time_step not in abutment_disps:
        print(f"警告：指定的时间步 {time_step} 不存在")
        return

    # 获取该时间步的数据
    step_data = abutment_disps[time_step]

    # 按桥台位置排序
    sorted_data = sorted(step_data, key=lambda x: (x['abutment_side'], x['node_tag']))

    # 准备输出内容
    output_lines = []
    output_lines.append(f"时间步: {time_step:.3f}s")
    output_lines.append("=" * 80)
    output_lines.append("桥台位置\t节点编号\tX坐标\tY坐标\tZ坐标\t位移X(mm)\t位移Y(mm)\t位移Z(mm)")
    output_lines.append("-" * 80)

    for data in sorted_data:
        # 将位移单位从m转换为mm
        disp_x_mm = data['disp_x'] * 1000
        disp_y_mm = data['disp_y'] * 1000
        disp_z_mm = data['disp_z'] * 1000

        line = f"{data['abutment_side']}\t{data['node_tag']}\t{data['x_coord']:.3f}\t{data['y_coord']:.3f}\t{data['z_coord']:.3f}\t{disp_x_mm:.3f}\t{disp_y_mm:.3f}\t{disp_z_mm:.3f}"
        output_lines.append(line)

    # 输出数据
    if output_file:
        with open(output_file, 'w') as f:
            for line in output_lines:
                f.write(line + '\n')
        print(f"\n桥台节点位移数据已保存至 {output_file}")
    else:
        # 输出到控制台
        print("\n桥台节点位移数据:")
        for line in output_lines:
            print(line)
