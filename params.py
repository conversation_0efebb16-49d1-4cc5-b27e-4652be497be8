import numpy as np
from utils.update_params import update_from_config
from utils.calculate_volume import calculate_girder_volume
from utils.calculate_weight import calculate_weight
from utils.calculate_pier_params import calculate_pier_params
import openseespy.opensees as ops


class BridgeParams:
    def __init__(self, config_file=None):
        # 几何参数
        self.num_spans = 3                 # 跨数
        self.span_lengths = [10, 22, 10]   # 各跨跨度(m)
        self.pier_heights = [2+2.5+0.5]*2  # 桥墩高度(m) 水面上+水深+埋深
        self.pier_segments = 6             # 桥墩单元数
        self.deck_width = 13               # 桥面宽度(m)
        self.girder = "hollow_slab"        # 梁截面类型
        self.rubber = "GBZJ200x200x35"     # 支座类型
        self.continuous_deck = True        # 是否为连续桥面
        self.block_transverse = True       # 是否有横桥向挡块

        # 横桥向布置参数
        self.num_piers_transverse = 3      # 横桥向桥墩数量
        self.pier_spacing_transverse = 6   # 横桥向桥墩间距(m)
        self.bearing_spacing = 0.5         # 支座间距(空心板梁宽度，m)

        # 混凝土材料参数
        self.concrete_materials = {
            "cover": {                     # 保护层混凝土（非约束）
                "grade": "C40",            # 混凝土标号
                "Ec": None,                # 弹性模量(MPa)
                "fc": None,                # 圆柱体抗压强度(MPa) - 0.79*40 (40=立方体抗压强度标准值)
                "ec": 0.002,               # 峰值应变
                "ecu": 0.005,              # 极限应变
                "ft": None,                # 抗拉强度(MPa)
                "Et": None,                # 受拉软化模量
                "gamma": 0.125,            # 张拉刚度系数 (服役开裂时降低)
            },
            "core": {                      # 核心区混凝土（约束）
                "grade": "C40",            # 混凝土标号
                "Ec": None,                # 弹性模量(MPa)
                "fc": None,                # 圆柱体抗压强度(MPa)
                "ec": 0.002,               # 峰值应变
                "ecu": None,               # 极限应变(参考: 0.008)
                "ft": None,                # 抗拉强度(MPa)
                "Et": None,                # 受拉软化模量
                "gamma": 0.125,            # 张拉刚度系数 (服役开裂时降低)
                "k": 1.4                   # 强度提高系数（约束效应）
            }
        }

        # 钢筋材料参数
        self.steel_materials = {
            "longitudinal": {              # 纵向钢筋
                "grade": "HRB400",
                "fy": 360,                 # 屈服强度(MPa)
                "fu": 540,                 # 极限强度(MPa)
                "eu": 0.09,                # 极限应变
                "Es": 200000.0             # 弹性模量(MPa)
            },
            "transverse": {                # 横向箍筋
                "grade": "HPB300",
                "fy": 270,                 # 屈服强度设计值(MPa)
                "fyk": 300,                # 屈服强度标准值(MPa)
                "fu": 400,                 # 极限强度(MPa)
                "eu": 0.09,                # 极限应变
                "Es": 200000.0             # 弹性模量(MPa)
            }
        }

        # 截面参数
        self.cap_beam_section = {
            "width": 1.4,                  # 盖梁宽度(m)
            "height": 1.38,                 # 盖梁高度(m)
            "rebar_dia": 0.020,            # 钢筋直径(m)
            "spacing": 0.2,                # 钢筋间距(m)
            "concrete_cover": 0.05,        # 保护层厚度(m)
        }
        self.pier_section = {
            "type": "circ",
            "diameter": 1.6,                # 桥墩直径(m)
            "concrete_cover": 0.04,         # 混凝土保护层厚度(m)
            "longitudinal_bars": {
                "diameter": 0.025,          # 纵向钢筋直径(m)
                "number": 48,               # 纵向钢筋数量(配筋率0.6%-4%)
                "material": "longitudinal", # 材料类型（对应steel_materials中的键）
                "rho": None                 # 配筋率(参考: 0.006~0.04)
            },
            "transverse_bars": {
                "diameter": 0.025,          # 箍筋直径(m) >=0.01
                "spacing": 0.10,            # 箍筋间距(m) <=0.1
                "material": "transverse",   # 材料类型（对应steel_materials中的键）
                "configuration": "hoop",    # 箍筋形式：spiral-螺旋箍筋，hoop-环形箍筋
                "rho": None                 # 体积配箍率(参考: 0.005)
            },
            "concrete": {
                "core": "core",             # 核心区混凝土材料（对应concrete_materials中的键）
                "cover": "cover"            # 保护层混凝土材料（对应concrete_materials中的键）
            }
        }
        self.girder_section = {
            "hollow_slab": {
                "type": "hollow_slab",
                "height": 0.9,                  # 空心板梁高度 (m)
                "slab_width": 1.0,              # 单个空心板宽度 (m)
                "hollow_width": 0.44,           # 空洞宽度 (m)
                "hollow_height": 0.45,          # 空洞高度 (m)
                "cover": 0.05,                  # 保护层厚度 (m)
                "n_long_rebar": 12,             # 每板纵向钢筋数
                "rebar_dia": 0.016              # 钢筋直径 (m)
            },
            "box": {
                "type": "box",
                "width": 6.0,                   # 梁截面宽度 (m)
                "height": 2.5,                  # 梁截面高度 (m)
                "top_slab_thickness": 0.35,     # 顶板厚 (m)
                "bottom_slab_thickness": 0.25,  # 底板厚 (m)
                "web_thickness": 0.4,           # 腹板厚 (m)
                'n_cells': 1,                   # 单箱单室
                "cover": 0.05,                  # 保护层厚度 (m)
                "n_long_rebar": 12,             # 每腹板纵向钢筋数
                "rebar_dia": 0.025              # 钢筋直径 (m)
            },
        }

        # 阻尼比
        self.damping_ratio = 0.05

        # 荷载参数
        self.gravity = 9.81              # 重力加速度 (m/s²)
        self.concrete_density = 2500     # kg/m³
        self.steel_density = 7850        # kg/m³

        # 桥台填土参数
        self.abutment = {
            "backwall_height": 2.0,      # 背墙高度 (m)
            "gap": None,                 # 伸缩缝宽度 (m)
            # 考虑桥台—填土—结构相互作用时RC连续梁桥抗震性能研究
            # "backfill_E": 200e6,       # 填土切线刚度 (N/m)
            # "backfill_fy": 5e6,        # 填土极限抗力 (N)
        }

        # 橡胶支座参数
        self.bearing = {
            "friction": True,               # 是否使用弹塑性材料模拟支座摩擦滑动
            "friction_coef": 0.25,          # 支座摩擦系数（混凝土接触面）

            "GBZJ200x200x35": {
                "type": "GBZJ200x200x35",
                "G":    1000e3,             # 动剪切模量 (N/m²)
                "A":    0.04,               # 剪切面积 (m²)
                "t":    0.035,              # 总厚度 (m)
                "te":   0.025,              # 橡胶层总厚度 (m)
                "t0":   0.002,              # 单层钢板厚度 (m)
                "t1":   0.005,              # 单层橡胶层厚度 (m)
                "s":    9.5,                # 形状系数  
                "Rck":  361*1e3,            # 最大承压力 (N)
            },
            "GBZJ300x350x52": {
                "type": "GBZJ300x350x52",
                "G":    1000e3,             # 动剪切模量 (N/m²)
                "A":    0.105,              # 剪切面积 (m²)
                "t":    0.052,              # 总厚度 (m)
                "te":   0.037,              # 橡胶层总厚度 (m)
                "t0":   0.003,              # 单层钢板厚度 (m)
                "t1":   0.008,              # 单层橡胶层厚度 (m)
                "s":    9.78,               # 形状系数
                "Rck":  986*1e3,            # 最大承压力 (N)
            }
        }
        self.super_weight = None
        self.sub_weight = None
        
        update_from_config(config_file, self)
        self.update_pier_heights()
        self.calculate_gap()
        self.girder_volume = calculate_girder_volume(self)
        calculate_weight(self)
        calculate_pier_params(self)

        # 事故 - 抗震验算 (CJJ 166-2011 城市桥梁抗震设计规范)
        self.accidents = {
            "girder_falling": girder_falling_limit(self, degree=6),  # 落梁
            "pier_top_displacement": pier_top_d_limit(self),         # 桥墩位移破坏
            "bearing_failure": bearing_failure_limit(self)           # 支座剪切破坏
        }
        self.validate_params()


    def validate_params(self):
        assert len(self.span_lengths) == self.num_spans, "跨长数量与跨数不匹配"
        assert self.num_piers_transverse >= 1, "横桥向桥墩数量必须大于等于1"
        assert self.pier_spacing_transverse > 0, "横桥向桥墩间距必须大于0"
        assert self.bearing_spacing > 0, "支座间距必须大于0"
        assert self.pier_section['etak'] < 0.3, f"轴压比 {self.pier_section['etak']:.4f} 超出规范要求(0.3)"

        # 检查箍筋间距
        transverse_spacing = self.pier_section['transverse_bars']["spacing"]
        # steel_t_params = self.steel_materials["transverse"]
        steel_l_params = self.steel_materials["longitudinal"]
        ts_limit = (3 + 6*steel_l_params["fu"]/steel_l_params["fy"]) * \
            self.pier_section['longitudinal_bars']["diameter"]
        assert transverse_spacing <= ts_limit, f"箍筋间距 {transverse_spacing:.2f}m 超出规范要求"
        
        # 检查墩柱体积配箍率 >= rhos_min >= 0.004
        rho_s = self.pier_section['transverse_bars']["rho"]
        assert rho_s >= self.pier_section['rhos_min'], f"墩柱体积配箍率 {rho_s:.4f} 低于规范要求"

        # 检查墩柱纵向钢筋配筋率 0.006 ~ 0.04
        rebar_ratio = self.pier_section['longitudinal_bars']['rho']
        assert 0.006 <= rebar_ratio <= 0.04, f"墩柱纵向钢筋配筋率 {rebar_ratio:.4f} 超出规范范围(0.006~0.04)"

        return


    def update_pier_heights(self):
        """桥墩计算模型高度"""
        h_base = self.pier_heights
        h_cap = self.cap_beam_section["height"]
        h_girder = self.girder_section[self.girder]["height"]/2
        self.pier_heights = [h0+h_cap+h_girder for h0 in h_base]
        return


    def calculate_gap(self):
        """计算伸缩缝宽度"""
        if self.continuous_deck:
            l = np.sum(self.span_lengths)
        else:
            l = np.max(self.span_lengths)
        if l < 20:
            gap = 0.04
        elif l < 30:
            gap = 0.06
        else:
            gap = 0.08
        self.abutment["gap"] = gap
        print("伸缩缝宽度: {:.2f} m".format(gap))
        return


def girder_falling_limit(params, degree=6):
    # 支座抗滑稳定性验算 - 计算(相对位移-屈服位移)限值
    # 公路工程抗震设计规范(JTJ004-89)
    d0 = 50  # 梁端至盖梁边缘距离(cm)
    d0 = np.minimum(d0, params.cap_beam_section["width"]/2*100)  # (cm)
    limit = list(0.01*(d0 + np.array(params.span_lengths)))  # (m)
    return limit


def pier_top_d_limit(params):
    """计算桥墩顶部位移限值"""
    if params.num_spans == 1:
        return 0
    pier  = params.pier_section
    steel = params.steel_materials["longitudinal"]
    D     = pier['diameter']                            # (m)
    ey    = steel["fy"] / steel["Es"]                   # 屈服应变
    phiy  = 2.213 * ey / D                              # 截面屈服曲率(1/m)
    phiu = params.pier_section['phiu']                  # 截面极限曲率(1/m)

    H = np.mean(params.pier_heights)                    # 桥墩高度 (m)
    long_dia = pier["longitudinal_bars"]["diameter"]    # 纵向钢筋直径 (m)
    lp = 0.08 * H + 0.022 * steel["fy"] * long_dia      # 塑性铰长度 (m)
    assert lp >= 0.044 * steel["fy"] * long_dia         # 检查是否满足规范要求 7.3.5-2(p56)

    theta_u = lp * (phiu-phiy) / 2.0                    # 塑性铰最大容许转角
    d_limit = phiy*H*H/3 + theta_u*(H-lp/2)
    print("桥墩顶部位移限值: {:.4f} m".format(d_limit))
    return d_limit


def bearing_failure_limit(params):
    """计算支座容许剪切变形限值"""
    # gamma 容许剪切应变
    gamma = np.array([0.7, 1.5, 2.5, 4.5])
    # state0: 无破坏    (u < limit[0])
    # state1: 轻微破坏  (limit[0] <= u < limit[1])
    # state2: 中等破坏  (limit[1] <= u < limit[2])
    # state3: 严重破坏  (limit[2] <= u < limit[3])
    # state4: 毁坏      (u >= limit[3])
    limits = list(gamma * params.bearing[params.rubber]["te"])
    return limits
