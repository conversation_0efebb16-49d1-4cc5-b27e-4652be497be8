"""
Simply Supported Bridge Deck Module

This module handles the creation and configuration of discontinuous bridge deck elements.
"""

import openseespy.opensees as ops
import numpy as np


def create_deck(model):
    """Generate discontinuous bridge deck structure (nodes + elements)"""
    generate_deck_nodes(model)
    generate_deck_elements(model)
    
    # 模拟桥台横向挡块
    ops.fix(model.span_nodes[0]['start'], 0, 1, 0, 0, 0, 0)
    ops.fix(model.span_nodes[max(model.span_nodes.keys())]['end'], 0, 1, 0, 0, 0, 0)


def generate_deck_nodes(model):
    """Automatically generate deck nodes for discontinuous spans"""
    x = 0.0
    spacing = model._calculate_mesh_spacing()

    # Track span start/end nodes for each span
    model.span_nodes = {}

    for span_idx, span in enumerate(model.params.span_lengths):
        span_start_node = None
        span_end_node = None

        # Create nodes for this span
        num_nodes = max(2, int(np.ceil(span / spacing)))
        x_coords = np.linspace(x, x+span, num_nodes, endpoint=True)

        # Create nodes for this span
        span_nodes = []
        for x_pos in x_coords:
            node_tag = model._next_tag('node')
            ops.node(node_tag, x_pos, 0.0, 0.0)
            span_nodes.append(node_tag)

            # Track span start/end nodes
            if np.isclose(x_pos, x):
                span_start_node = node_tag
            if np.isclose(x_pos, x+span):
                span_end_node = node_tag

        # Store all nodes for this span
        model.span_nodes[span_idx] = {
            'start': span_start_node,
            'end': span_end_node,
            'all': span_nodes
        }

        # Add all nodes to deck nodes list
        model.deck['nodes'].extend(span_nodes)

        # Mark support nodes (all span start/end nodes are potential support locations)
        model.supports.append(span_start_node)
        model.supports.append(span_end_node)

        x += span


def generate_deck_elements(model):
    """Create deck beam elements for each span"""
    girder_section = model.sections['Deck']

    # Process each span separately
    for span_idx, span_data in model.span_nodes.items():
        span_nodes = span_data['all']

        # Create elements within this span
        for i in range(len(span_nodes) - 1):
            n_i = span_nodes[i]
            n_j = span_nodes[i+1]

            # Validate node coordinates
            x_i = ops.nodeCoord(n_i, 1)
            x_j = ops.nodeCoord(n_j, 1)
            if np.isclose(x_i, x_j):
                raise ValueError(f"Nodes {n_i} and {n_j} have duplicate coordinates")

            # Create element with consistent mass matrix support
            elem_tag = model._next_tag('element')

            # Use standard elastic beam column element
            ops.element('elasticBeamColumn', elem_tag,
                        n_i, n_j,
                        girder_section,
                        model.transf_tag_beam)

            # Store element type for reference
            if not hasattr(model, 'element_types'):
                model.element_types = {}
            model.element_types[elem_tag] = 'elasticBeamColumn'

            model.deck['elements'].append(elem_tag)

