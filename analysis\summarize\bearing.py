"""
Bearing Summarize Module

This module contains functions for summarizing and visualizing bearing analysis results.
"""

from utils.bearing_relative_displacement_output import output_bearing_relative_displacements
from utils.bearing_relative_displacement_save import save_bearing_relative_displacements_history
from utils.bearing_relative_displacement_plot import plot_bearing_relative_displacements as plot_bearing_disps


def summarize_bearing_relative_displacements(bearing_relative_disps):
    """Output a summary of bearing relative displacement data
    
    Args:
        bearing_relative_disps: Dictionary of bearing relative displacement data
    """
    print("\n支座相对位移数据")
    # 检查是否有数据
    if not bearing_relative_disps:
        print("警告：没有支座相对位移数据可供输出")
        return

    # 输出支座相对位移数据
    output_bearing_relative_displacements(
        bearing_relative_disps, output_file='results/bearing_relative_disps.txt')

    # 保存支座相对位移历史数据
    csv_file = 'results/bearing_relative_disps.csv'
    save_bearing_relative_displacements_history(bearing_relative_disps, csv_file)

    # 绘制所有支座的相对位移时程曲线
    plot_all_bearing_relative_displacements(bearing_relative_disps)


def plot_all_bearing_relative_displacements(bearing_relative_disps):
    """Plot relative displacements for all bearings in all directions
    
    Args:
        bearing_relative_disps: Dictionary of bearing relative displacement data
    """
    # 为X、Y、Z三个方向分别绘制图形
    for direction, dir_name in [(1, 'X'), (2, 'Y'), (3, 'Z')]:
        plot_file = f'results/bearing_relative_disps_{dir_name}.png'
        plot_bearing_disps(bearing_relative_disps, plot_file, direction)
        print(f"- 支座相对位移时程曲线已保存至 {plot_file}")
