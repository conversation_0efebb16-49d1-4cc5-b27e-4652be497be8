"""
支座相对位移绘图模块

该模块提供函数用于绘制支座相对位移随时间的变化曲线。
"""

import matplotlib.pyplot as plt


def plot_bearing_relative_displacements(bearing_relative_disps, output_file="bearing_relative_disps.png", direction=1):
    """绘制支座相对位移随时间的变化

    为每跨支座绘制一个subplot，不同跨的subplot纵向排列，图例分为两列

    参数:
        bearing_relative_disps: 支座相对位移数据字典 {time: [bearing_data, ...]}
        output_file: 输出文件路径
        direction: 位移方向 1=X, 2=Y, 3=Z
    """
    # 检查是否有数据
    if not bearing_relative_disps:
        print("警告：没有支座相对位移数据可供绘制")
        return

    # 确定方向标签
    dir_labels = {1: 'X', 2: 'Y', 3: 'Z'}
    dir_label = dir_labels.get(direction, 'X')

    # 准备数据
    times = sorted(bearing_relative_disps.keys())

    # 获取所有支座的唯一标识
    bearing_ids = set()
    for time in times:
        for data in bearing_relative_disps[time]:
            bearing_ids.add((data['span'], data['bearing_idx']))

    # 按跨号分组支座
    spans_dict = {}
    for span, bearing_idx in sorted(bearing_ids):
        if span not in spans_dict:
            spans_dict[span] = []
        spans_dict[span].append(bearing_idx)

    # 获取跨数
    spans = sorted(spans_dict.keys())
    num_spans = len(spans)

    # 创建图形 - 根据跨数调整图形高度
    fig_height = 4 * num_spans  # 每个子图高度为4英寸
    _, axs = plt.subplots(num_spans, 1, figsize=(12, fig_height), dpi=150, sharex=True)

    # 如果只有一个跨，确保axs是列表
    if num_spans == 1:
        axs = [axs]

    # 为每个跨创建一个子图
    for i, span in enumerate(spans):
        ax = axs[i]

        # 获取该跨的所有支座
        bearings = spans_dict[span]

        # 为该跨的每个支座绘制一条曲线
        for bearing_idx in sorted(bearings):
            # 收集该支座的时间历程数据
            disp_data = []
            for time in times:
                # 查找该支座在当前时间步的数据
                for data in bearing_relative_disps[time]:
                    if data['span'] == span and data['bearing_idx'] == bearing_idx:
                        # 根据方向选择位移数据
                        if direction == 1:
                            disp_data.append(data['rel_disp_x'] * 1000)  # 转换为mm
                        elif direction == 2:
                            disp_data.append(data['rel_disp_y'] * 1000)
                        else:
                            disp_data.append(data['rel_disp_z'] * 1000)
                        break
                else:
                    # 如果在当前时间步找不到该支座的数据，则使用0
                    disp_data.append(0.0)

            # 绘制曲线
            ax.plot(times, disp_data, linewidth=1., label=f'bearing{bearing_idx}')

        # 子图装饰
        ax.set_title(f"Span {span} - Relative Displacement ({dir_label} direction)", fontsize=12, fontweight='bold')
        ax.set_ylabel(f"Displacement ({dir_label}) (mm)", fontsize=10)
        ax.grid(True, linestyle='--', alpha=0.6)

        # 为每个子图添加图例
        ax.legend(fontsize='small', loc='upper right', ncol=4)

    # 为最后一个子图添加x轴标签
    axs[-1].set_xlabel("Time (s)", fontsize=10)

    # 调整子图间距
    plt.tight_layout()

    # 保存图形
    plt.savefig(output_file, bbox_inches='tight')  # 使用bbox_inches确保图例完全显示
    plt.close()
