"""
Pier Summarize Module

This module contains functions for summarizing pier analysis results
and checking for pier displacement accidents using data from recorder files.
"""

import os
import numpy as np
from analysis import check_pier_displacement_accident
from utils.pier_relative_displacement_plot import plot_pier_relative_displacements as plot_pier_disps


def read_pier_displacement_data(recorder_info, output_dir='results'):
    """Read pier displacement data from recorder files

    Args:
        recorder_info: Dictionary with recorder information
        output_dir: Directory where recorder files are stored

    Returns:
        dict: Dictionary of pier relative displacement data in the format {time: [pier_data, ...]}
    """
    # Check if recorder_info is valid
    if not recorder_info or 'piers' not in recorder_info:
        print("警告: 没有有效的记录器信息，无法读取桥墩位移数据")
        return {}

    # Dictionary to store pier displacement data
    pier_data = {}

    # Process each pier in recorder_info
    for pier_id, pier_info in recorder_info['piers'].items():
        # Get pier information
        pier_key = pier_info['pier_key']
        x_coord = pier_info['x_coord']
        y_coord = pier_info['y_coord']

        # Get displacement file path
        disp_file = pier_info['disp_file']

        # Check if file exists
        if not os.path.exists(disp_file):
            print(f"警告: 文件 {disp_file} 不存在，跳过该桥墩")
            continue

        try:
            # Read displacement data
            data = np.loadtxt(disp_file)

            # Check if data is empty
            if data.size == 0:
                print(f"警告: 文件 {disp_file} 中没有数据，跳过该桥墩")
                continue

            # Extract data
            times = data[:, 0]
            disp_x = data[:, 1]
            disp_y = data[:, 2]
            disp_z = data[:, 3]

            # Process each time step
            for i, time in enumerate(times):
                # Round time to 3 decimal places to match analyzer time steps
                time_rounded = round(time, 3)

                # Initialize time step data if needed
                if time_rounded not in pier_data:
                    pier_data[time_rounded] = []

                # Create pier data dictionary
                pier_info = {
                    'pier_key': pier_key,
                    'pier_long_idx': pier_key[0],
                    'pier_trans_idx': pier_key[1],
                    'x_coord': x_coord,
                    'y_coord': y_coord,
                    'rel_disp_x': disp_x[i],
                    'rel_disp_y': disp_y[i],
                    'rel_disp_z': disp_z[i],
                    'top_node': pier_info['top_node'],
                    'bottom_node': pier_info['bottom_node']
                }

                # Add to time step data
                pier_data[time_rounded].append(pier_info)

        except Exception as e:
            print(f"读取文件 {disp_file} 时出错: {e}")
            continue

    return pier_data


def summarize_pier(recorder_info, model=None, params=None, output_dir='results'):
    """Output a summary of pier relative displacement data and check for pier displacement accidents

    Args:
        recorder_info: Dictionary with recorder information
        model: Bridge model object
        params: Bridge parameters object
        output_dir: Directory to store output files

    Returns:
        bool: True if pier displacement accident occurred, False otherwise
    """
    print("\n桥墩相对位移数据")

    # Read pier displacement data from recorder files
    pier_relative_disps = read_pier_displacement_data(recorder_info, output_dir)

    # 检查是否有数据
    if not pier_relative_disps:
        print("警告：没有桥墩相对位移数据可供输出")
        return False

    # 绘制桥墩相对位移时程曲线
    for direction, dir_name in [(1, 'X'), (2, 'Y'), (3, 'Z')]:
        plot_file = f'{output_dir}/pier_relative_disps_{dir_name}.png'
        plot_pier_disps(pier_relative_disps, plot_file, direction)

    # 检查桥墩位移超限事故
    return check_pier_displacement_accident(pier_relative_disps, model, params)


# def check_pier_displacement_accident(pier_relative_disps, model=None, params=None):
#     """检查是否发生桥墩位移超限事故

#     Args:
#         pier_relative_disps: Dictionary of pier relative displacement data
#         model: Bridge model object
#         params: Bridge parameters object

#     Returns:
#         bool: True if pier displacement accident occurred, False otherwise
#     """
#     # 检查模型和参数
#     if model is None and params is None:
#         print("错误: 必须提供模型对象或参数对象")
#         return False

#     # 获取桥墩位移限值
#     if params is None:
#         params = model.params

#     # 获取桥墩位移限值
#     pier_disp_limit = params.accidents.get("pier_top_displacement")
#     if pier_disp_limit is None:
#         print("警告: 未找到桥墩位移限值，无法检查桥墩位移超限事故")
#         return False

#     # 检查每个时间步的桥墩相对位移
#     accident_time = float('inf')
#     accident_pier = None

#     for time, time_data in sorted(pier_relative_disps.items()):
#         for pier in time_data:
#             pier_key = pier['pier_key']
#             pier_long_idx = pier_key[0]
#             pier_trans_idx = pier_key[1]
#             x_coord = pier['x_coord']
#             y_coord = pier['y_coord']

#             # 获取相对位移
#             rel_disp_x = pier['rel_disp_x']
#             rel_disp_y = pier['rel_disp_y']

#             # 计算水平位移合力 (x和y方向的合力)
#             horizontal_disp = np.sqrt(rel_disp_x**2 + rel_disp_y**2)

#             # 判断是否超限
#             is_exceeding = horizontal_disp > pier_disp_limit

#             if is_exceeding and time < accident_time:
#                 accident_time = time
#                 accident_pier = {
#                     'time': time,
#                     'pier_key': pier_key,
#                     'pier_long_idx': pier_long_idx,
#                     'pier_trans_idx': pier_trans_idx,
#                     'x_coord': x_coord,
#                     'y_coord': y_coord,
#                     'rel_disp_x': rel_disp_x,
#                     'rel_disp_y': rel_disp_y,
#                     'horizontal_disp': horizontal_disp,
#                     'limit': pier_disp_limit
#                 }

#     # 输出结果
#     if accident_pier:
#         print("\n发生桥墩位移超限事故!")
#         print(f"最早发生超限的时刻: {accident_pier['time']:.3f}s")
#         print(f"超限桥墩位置: ({accident_pier['pier_long_idx']},{accident_pier['pier_trans_idx']})")
#         print(f"超限桥墩坐标: ({accident_pier['x_coord']:.3f}, {accident_pier['y_coord']:.3f})")
#         print(f"位移限值: {accident_pier['limit']:.3f}m")
#         print(f"X方向相对位移: {accident_pier['rel_disp_x']:.3f}m")
#         print(f"Y方向相对位移: {accident_pier['rel_disp_y']:.3f}m")
#         print(f"水平位移合力: {accident_pier['horizontal_disp']:.3f}m")

#         return True
#     else:
#         print("\n未发生桥墩位移超限事故")
#         return False
