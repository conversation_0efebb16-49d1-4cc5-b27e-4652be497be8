"""
主梁节点位移绘图模块

该模块提供函数用于绘制主梁节点位移随时间的变化曲线。
"""

import matplotlib.pyplot as plt
import numpy as np


def plot_deck_displacements(deck_disps, output_file="deck_disps.png", direction=1):
    """绘制主梁节点位移随时间的变化

    参数:
        deck_disps: 主梁节点位移数据字典 {time: [deck_data, ...]}
        output_file: 输出文件路径
        direction: 位移方向 1=X, 2=Y, 3=Z
    """
    # 检查是否有数据
    if not deck_disps:
        print("警告：没有主梁节点位移数据可供绘制")
        return

    # 确定方向标签
    dir_labels = {1: 'X', 2: 'Y', 3: 'Z'}
    dir_label = dir_labels.get(direction, 'X')

    # 准备数据
    times = sorted(deck_disps.keys())

    # 获取所有主梁节点的唯一标识
    deck_ids = set()
    for time in times:
        for data in deck_disps[time]:
            deck_ids.add((data['deck_side'], data['node_tag']))

    # 创建图形
    plt.figure(figsize=(12, 8), dpi=150)

    # 为每个主梁节点绘制一条曲线
    for deck_id in sorted(deck_ids):
        deck_side, node_tag = deck_id
        
        # 提取该主梁节点的位移数据
        disp_data = []
        for time in times:
            for data in deck_disps[time]:
                if (data['deck_side'], data['node_tag']) == deck_id:
                    if direction == 1:
                        disp_data.append(data['disp_x'] * 1000)  # 转换为mm
                    elif direction == 2:
                        disp_data.append(data['disp_y'] * 1000)  # 转换为mm
                    elif direction == 3:
                        disp_data.append(data['disp_z'] * 1000)  # 转换为mm
                    break
            else:
                # 如果在当前时间步找不到该节点的数据，则使用0
                disp_data.append(0.0)
        
        # 绘制曲线
        label = f'{deck_side} deck (node {node_tag})'
        plt.plot(times, disp_data, label=label, linewidth=1.5)

    # 添加图例
    plt.legend(loc='best')

    # 图面装饰
    plt.title(f"Deck Displacement in {dir_label} Direction", fontsize=14, fontweight='bold')
    plt.xlabel("Time (s)", fontsize=12)
    plt.ylabel(f"Displacement ({dir_label}) (mm)", fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.tight_layout()
    
    # 保存图形
    plt.savefig(output_file)
    plt.close()
    
    print(f"- 主梁节点位移时程曲线已保存至 {output_file}")
