"""
Bridge Response Recorder Module

This package contains modules for recording time history responses of bridge components
during dynamic analysis using OpenSees recorders.
"""

import openseespy.opensees as ops

from analysis.recorder.pier import (
    setup_pier_recorders,
    find_piers_closest_to_y0
)

from analysis.recorder.abutment import (
    setup_abutment_recorders,
    read_abutment_displacement_data
)

from analysis.recorder.deck import (
    setup_deck_recorders,
    read_deck_displacement_data,
    find_first_nodes_of_each_span
)

from analysis.recorder.bearing import (
    setup_bearing_recorders,
    read_bearing_force_data,
    read_bearing_deformation_data,
    get_bearing_hysteresis_data
)


def remove_recorders(recorder_info):
    """
    Remove all recorders from a unified recorder_info dictionary.

    This function can handle recorder_info dictionaries that contain
    recorders from piers, abutments, and deck components.

    Args:
        recorder_info: Dictionary with recorder information containing 'recorder_tags' list
    """
    if not recorder_info:
        return

    recorder_tags = recorder_info.get('recorder_tags', [])

    for tag in recorder_tags:
        try:
            ops.remove('recorder', tag)
        except:
            pass

    print(f"已移除 {len(recorder_tags)} 个记录器")
