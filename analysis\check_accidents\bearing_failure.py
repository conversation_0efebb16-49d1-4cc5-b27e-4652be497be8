"""
支座剪切破坏检查模块

该模块用于检查桥梁在地震作用下是否发生支座剪切破坏。
剪切变形定义为相对位移与屈服位移的差值
根据规范，当支座剪切变形超过限值时，认为发生支座剪切破坏。
根据破坏程度分为轻微破坏、中等破坏、严重破坏和毁坏四个等级。
"""

# import os
# import csv
import numpy as np
from analysis.utils.data_readers import read_bearing_displacements


def check_bearing_failure(bearing_data, model=None, params=None):
    # 检查模型和参数
    if model is None and params is None:
        print("错误: 必须提供模型对象或参数对象")
        return False

    # 获取支座剪切破坏限值
    if params is None:
        params = model.params

    # 获取支座剪切破坏限值
    bearing_failure_limits = params.accidents.get("bearing_failure")
    if bearing_failure_limits is None or not isinstance(bearing_failure_limits, list):
        print("警告: 未找到支座剪切破坏限值，无法检查支座剪切破坏")
        return False

    # 获取支座屈服位移信息
    bearing_yield_disps = {}

    if hasattr(params, 'bearing_materials') and params.bearing_materials:
        for elem_tag, info in params.bearing_materials.items():
            bearing_yield_disps[elem_tag] = info['yield_disp']
    else:
        if params.bearing["friction"]:
            print("警告: 未找到支座屈服位移信息, 将使用0作为屈服位移")

    # # 读取支座相对位移数据
    # bearing_data = read_bearing_displacements(bearing_disps_file, model)
    # if not bearing_data:
    #     print(f"错误: 无法从 {bearing_disps_file} 读取支座相对位移数据")
    #     return False

    # 定义破坏等级描述
    damage_levels = {
        0: "无破坏",
        1: "轻微破坏",
        2: "中等破坏",
        3: "严重破坏",
        4: "毁坏"
    }

    # 创建一个字典，用于存储每个支座的最大破坏等级
    # 键为 (span, bearing_idx)，值为包含破坏信息的字典
    bearing_damages = {}

    # 检查每个时间步的支座相对位移
    for time, time_data in sorted(bearing_data.items()):
        for bearing in time_data:
            span = bearing['span']
            x_coord = bearing['x_coord']
            y_coord = bearing['y_coord']
            bearing_idx = bearing['bearing_idx']
            elem_tag = bearing.get('elem_tag')
            bearing_key = (span, bearing_idx)

            # 获取相对位移 (mm -> m)
            rel_disp_x = bearing['rel_disp_x']  # 传入原始数据单位为m, csv文件中为mm
            rel_disp_y = bearing['rel_disp_y']  # 传入原始数据单位为m, csv文件中为mm

            # 计算水平相对位移合力
            rel_disp_h = np.sqrt(rel_disp_x**2 + rel_disp_y**2)

            # 获取屈服位移
            yield_disp = bearing_yield_disps.get(elem_tag, 0.0) if elem_tag else 0.0

            # 计算相对位移与屈服位移的最小值
            if yield_disp > 0:
                u = np.minimum(rel_disp_h, yield_disp)
            else:
                u = rel_disp_h
                if params.bearing["friction"]:
                    raise ValueError(f"支座屈服位移为0")

            # 判断破坏等级
            damage_level = 0
            for i, limit in enumerate(bearing_failure_limits):
                if u >= limit:
                    damage_level = i + 1

            # 如果当前支座的破坏等级大于已记录的最大破坏等级，则更新
            if bearing_key not in bearing_damages or u > bearing_damages[bearing_key]['shear_deformation']:
                bearing_damages[bearing_key] = {
                    'time': time,
                    'span': span,
                    'bearing_idx': bearing_idx,
                    'x_coord': x_coord,
                    'y_coord': y_coord,
                    'rel_disp_x': rel_disp_x,
                    'rel_disp_y': rel_disp_y,
                    'rel_disp_h': rel_disp_h,
                    'yield_disp': yield_disp,
                    'shear_deformation': u,
                    'damage_level': damage_level,
                    'damage_description': damage_levels[damage_level]
                }

    # 输出结果
    print("\n支座剪切破坏检查结果:")

    # 检查是否有任何支座发生破坏
    has_damage = any(info['damage_level'] > 0 for info in bearing_damages.values())

    if not has_damage:
        print("所有支座均未发生剪切破坏")
        return False
    else:
        # 按跨号和支座编号排序
        sorted_damages = sorted(bearing_damages.values(), key=lambda x: (x['span'], x['bearing_idx']))

        # 输出表头
        print("\n{:<5} {:<5} {:<10} {:<10} {:<15} {:<15} {:<15} {:<15}".format(
            "跨号", "编号", "X坐标", "Y坐标", "最大变形(m)", "相对位移(m)", "屈服位移(m)", "破坏等级"))
        print("-" * 100)

        # 输出每个支座的破坏情况
        for damage_info in sorted_damages:
            if damage_info['damage_level'] > 0:  # 只输出有破坏的支座
                print("{:<5} {:<5} {:<10.3f} {:<10.3f} {:<15.3f} {:<15.3f} {:<15.3f} {:<15}".format(
                    damage_info['span'],
                    damage_info['bearing_idx'],
                    damage_info['x_coord'],
                    damage_info['y_coord'],
                    damage_info['shear_deformation'],
                    damage_info['rel_disp_h'],
                    damage_info['yield_disp'],
                    f"{damage_info['damage_level']} - {damage_info['damage_description']}"
                ))

        # 输出破坏限值说明
        print("\n破坏等级说明:")
        print("0 - 无破坏    (剪切变形 < {:.3f}m)".format(bearing_failure_limits[0]))
        print("1 - 轻微破坏  ({:.3f}m <= 剪切变形 < {:.3f}m)".format(bearing_failure_limits[0], bearing_failure_limits[1]))
        print("2 - 中等破坏  ({:.3f}m <= 剪切变形 < {:.3f}m)".format(bearing_failure_limits[1], bearing_failure_limits[2]))
        print("3 - 严重破坏  ({:.3f}m <= 剪切变形 < {:.3f}m)".format(bearing_failure_limits[2], bearing_failure_limits[3]))
        print("4 - 毁坏      (剪切变形 >= {:.3f}m)".format(bearing_failure_limits[3]))
    
        return True


if __name__ == "__main__":
    # 测试函数
    from params import BridgeParams
    params = BridgeParams()
    check_bearing_failure(params=params)
