import numpy as np


def calculate_weight(params):
    """计算上部结构重量"""
    # 主梁
    girder_volume = params.girder_volume
    girder_weight = girder_volume * params.concrete_density * params.gravity
    # 附加荷载: 铺装层 (假定250kg/m2)
    deck_area = params.deck_width * sum(params.span_lengths)
    deck_weight = 250 * deck_area * params.gravity
    # 附加荷载: 混凝土护栏 6kN/m 单侧
    guardrail_weight = 2 * 6 * sum(params.span_lengths)
    # 总重
    params.super_weight = girder_weight + deck_weight + guardrail_weight
    
    '''计算下部结构重量'''
    # 盖梁
    cap = params.cap_beam_section
    cap_volume = cap['width'] * cap['height'] * params.deck_width * (params.num_spans-1)
    cap_weight = cap_volume * params.concrete_density * params.gravity
    params.cap_weight = cap_weight
    # 桥墩
    if params.num_spans == 1:
        pier_weight = 0
    else:
        pier_height = np.sum(params.pier_heights) * params.num_piers_transverse
        pier_area   = np.pi * (params.pier_section['diameter']/2)**2 * pier_height
        pier_volume = pier_area * pier_height
        pier_weight = pier_volume * params.concrete_density * params.gravity
    # 总重
    params.pier_weight = pier_weight
    params.sub_weight = cap_weight + pier_weight
    
    return
    
