"""
支座相对位移保存模块

该模块提供函数用于保存支座相对位移历史数据到CSV文件。
"""


def save_bearing_relative_displacements_history(bearing_relative_disps, output_file):
    """保存所有时间步的支座相对位移历史数据到CSV文件

    参数:
        bearing_relative_disps: 支座相对位移数据字典 {time: [bearing_data, ...]}
        output_file: 输出文件路径
    """
    # 检查是否有数据
    if not bearing_relative_disps:
        print("警告：没有支座相对位移数据可供保存")
        return

    # 打开文件
    with open(output_file, 'w') as f:
        # 写入表头
        f.write("时间(s),跨号,支座编号,X坐标,Y坐标,相对位移X(mm),相对位移Y(mm),相对位移Z(mm)\n")

        # 按时间排序
        for time in sorted(bearing_relative_disps.keys()):
            # 获取该时间步的数据
            step_data = bearing_relative_disps[time]

            # 按跨号和Y坐标排序
            sorted_data = sorted(step_data, key=lambda x: (x['span'], x['y_coord']))

            # 写入数据
            for data in sorted_data:
                # 将位移单位从m转换为mm
                rel_disp_x_mm = data['rel_disp_x'] * 1000
                rel_disp_y_mm = data['rel_disp_y'] * 1000
                rel_disp_z_mm = data['rel_disp_z'] * 1000

                line = f"{time:.3f},{data['span']},{data['bearing_idx']},{data['x_coord']:.3f},{data['y_coord']:.3f},{rel_disp_x_mm:.3f},{rel_disp_y_mm:.3f},{rel_disp_z_mm:.3f}"
                f.write(line + '\n')

    print(f"- 支座相对位移历史数据已保存至 {output_file}")
