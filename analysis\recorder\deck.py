"""
Bridge Deck Response Recorder Module

This module provides functions for recording time history responses of bridge deck (main beam)
during dynamic analysis using OpenSees recorders.

The module records:
- Displacement of the first main beam node of each span
"""

import os
import openseespy.opensees as ops
import numpy as np


def find_first_nodes_of_each_span(model):
    """
    Find the first node of each span in the bridge deck.

    Args:
        model: Bridge model object

    Returns:
        dict: Dictionary mapping span index to first node tag
    """
    # Check if model has span_nodes attribute (for simply supported beam model)
    if hasattr(model, 'span_nodes'):
        first_nodes = {}
        for span_idx, span_data in model.span_nodes.items():
            first_nodes[span_idx] = span_data['start']
        return first_nodes
    
    # For continuous beam model, we need to identify spans based on coordinates
    elif model.deck['nodes']:
        # Get span lengths
        span_lengths = model.params.span_lengths
        
        # Calculate span boundaries
        span_boundaries = [0]
        for span in span_lengths:
            span_boundaries.append(span_boundaries[-1] + span)
        
        # Find first node in each span
        first_nodes = {}
        for span_idx, (start_x, end_x) in enumerate(zip(span_boundaries[:-1], span_boundaries[1:])):
            for node in model.deck['nodes']:
                x_coord = ops.nodeCoord(node, 1)
                if abs(x_coord - start_x) < 1e-6:  # Node is at span start
                    first_nodes[span_idx] = node
                    break
        
        return first_nodes
    
    return {}


def _setup_deck_recorder(model, span_idx, node_tag, dt, output_dir, recorder_info):
    """
    Set up OpenSees recorders for a single deck node.

    Args:
        model: Bridge model object
        span_idx: Span index
        node_tag: Deck node tag
        dt: Time step for recording
        output_dir: Directory to store recorder output files
        recorder_info: Dictionary to store recorder information

    Returns:
        dict: Updated recorder information
    """
    # Get node coordinates
    x_coord = ops.nodeCoord(node_tag, 1)
    y_coord = ops.nodeCoord(node_tag, 2)
    z_coord = ops.nodeCoord(node_tag, 3)

    # Create file name
    deck_id = f"deck_span{span_idx}_x{x_coord:.1f}"
    disp_file = f"{output_dir}/deck_disp_{deck_id}.txt"

    # Set up displacement recorder for deck node
    disp_recorder = ops.recorder('Node', '-file', disp_file, '-time', '-dT', dt,
                               '-node', node_tag, '-dof', 1, 2, 3, 'disp')

    # Store recorder information
    recorder_info['deck_nodes'][deck_id] = {
        'span_idx': span_idx,
        'node_tag': node_tag,
        'x_coord': x_coord,
        'y_coord': y_coord,
        'z_coord': z_coord,
        'disp_file': disp_file
    }

    recorder_info['recorder_tags'].append(disp_recorder)

    return recorder_info


def setup_deck_recorders(model, dt, output_dir='results', recorder_info=None):
    """
    Set up OpenSees recorders for the first main beam node of each span.

    Args:
        model: Bridge model object
        dt: Time step for recording
        output_dir: Directory to store recorder output files
        recorder_info: Existing recorder information dictionary to update

    Returns:
        dict: Dictionary with recorder information
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Check if there are deck nodes in the model
    if not model.deck['nodes']:
        print("警告: 模型中没有主梁节点，无法设置主梁响应记录器")
        return recorder_info if recorder_info is not None else {}

    # Initialize recorder_info if not provided
    if recorder_info is None:
        recorder_info = {
            'deck_nodes': {},
            'recorder_tags': []
        }
    else:
        # Ensure required keys exist
        if 'deck_nodes' not in recorder_info:
            recorder_info['deck_nodes'] = {}
        if 'recorder_tags' not in recorder_info:
            recorder_info['recorder_tags'] = []

    # Find first node of each span
    first_nodes = find_first_nodes_of_each_span(model)
    
    if not first_nodes:
        print("警告: 无法确定各跨第一个主梁节点，无法设置主梁响应记录器")
        return {}

    # Set up recorders for each span's first node
    initial_recorder_count = len(recorder_info['recorder_tags'])
    for span_idx, node_tag in first_nodes.items():
        recorder_info = _setup_deck_recorder(
            model, span_idx, node_tag, dt, output_dir, recorder_info
        )

    deck_recorder_count = len(recorder_info['recorder_tags']) - initial_recorder_count
    print(f"已设置 {deck_recorder_count} 个主梁响应记录器")
    return recorder_info


def read_deck_displacement_data(recorder_info, output_dir='results'):
    """
    Read deck displacement data from recorder files.

    Args:
        recorder_info: Dictionary with recorder information
        output_dir: Directory where recorder files are stored

    Returns:
        dict: Dictionary of deck displacement data in the format {time: [deck_data, ...]}
    """
    # Check if recorder_info is valid
    if not recorder_info or 'deck_nodes' not in recorder_info:
        print("警告: 没有有效的记录器信息，无法读取主梁位移数据")
        return {}

    # Dictionary to store deck displacement data
    deck_disps = {}

    # Process each deck node in recorder_info
    for deck_id, deck_info in recorder_info['deck_nodes'].items():
        # Get deck node information
        span_idx = deck_info['span_idx']
        node_tag = deck_info['node_tag']
        x_coord = deck_info['x_coord']
        y_coord = deck_info['y_coord']
        z_coord = deck_info['z_coord']

        # Get displacement file path
        disp_file = deck_info['disp_file']

        # Check if file exists
        if not os.path.exists(disp_file):
            print(f"警告: 文件 {disp_file} 不存在，跳过该主梁节点")
            continue

        try:
            # Read displacement data
            data = np.loadtxt(disp_file)

            # Check if data is empty
            if data.size == 0:
                print(f"警告: 文件 {disp_file} 中没有数据，跳过该主梁节点")
                continue

            # Handle single row data
            if data.ndim == 1:
                data = data.reshape(1, -1)

            # Process each time step
            for row in data:
                time = row[0]
                disp_x = row[1]
                disp_y = row[2]
                disp_z = row[3]

                # Initialize time step data if needed
                if time not in deck_disps:
                    deck_disps[time] = []

                # Add deck data for this time step
                deck_disps[time].append({
                    'deck_side': f'span{span_idx}',
                    'node_tag': node_tag,
                    'x_coord': x_coord,
                    'y_coord': y_coord,
                    'z_coord': z_coord,
                    'disp_x': disp_x,
                    'disp_y': disp_y,
                    'disp_z': disp_z
                })

        except Exception as e:
            print(f"读取文件 {disp_file} 时出错: {e}")
            continue

    return deck_disps

