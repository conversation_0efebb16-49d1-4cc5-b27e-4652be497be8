import openseespy.opensees as ops


def set_steel(params, mat_tags):
    """设置钢筋材料模型"""
    # 纵向钢筋
    set_reinforcement(
        mat_tag=mat_tags["SteelLongitudinal"],
        steel_params=params.steel_materials["longitudinal"]
    )

    # 横向钢筋
    set_reinforcement(
        mat_tag=mat_tags["SteelTransverse"],
        steel_params=params.steel_materials["transverse"]
    )
    
    
def set_reinforcement(mat_tag, steel_params):
    """设置钢筋材料模型"""
    fy = steel_params["fy"] * 1e6       # 屈服强度（Pa）
    fu = steel_params["fu"] * 1e6       # 极限强度（Pa）
    Es = steel_params["Es"] * 1e6       # 弹性模量（Pa）
    eu = steel_params["eu"]             # 极限应变

    # 计算硬化参数
    Eh = (fu - fy) / (eu - fy/Es)       # 硬化模量
    b = Eh / Es                         # 屈服后斜率与初始弹性模量的比值

    # 创建Steel02材料
    ops.uniaxialMaterial('Steel02', mat_tag,
                        fy,                    # 屈服强度
                        Es,                    # 弹性模量
                        b,                     # 硬化强化率
                        20.0,                  # 控制从弹性到塑性转变的参数R0
                        0.925,                 # 控制转变曲线形状的参数cR1
                        0.15)                  # 控制转变曲线形状的参数cR2
                        