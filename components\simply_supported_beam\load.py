"""
Simply Supported Bridge Load Module

This module handles the application of loads to bridge components.
"""

import openseespy.opensees as ops
import numpy as np
from components.simply_supported_beam.mass import apply_mass


def apply_loads(model):
    """Apply loads to the bridge model"""
    ops.remove('loadPattern', 0)  # Clear default pattern
    ops.timeSeries("Linear", 1)   # Time series tag=1
    ops.pattern("Plain", 1, 1)    # Pattern tag=1, time series=1

    apply_mass(model)
    apply_self_weight(model)  # Self-weight load function
    # apply_live_load(model)    # Live load function


def apply_self_weight(model):
    """Calculate self-weight loads for deck and piers"""
    total_loads = {}
    super_weight = model.params.super_weight

    # 将主梁重量按跨长分配到各跨
    # 计算每个跨的重量
    total_length = sum(model.params.span_lengths)
    span_weights = {i: (length / total_length) * super_weight
                    for i, length in enumerate(model.params.span_lengths)}

    # 将主梁重量分配到主梁节点上（而非支座上）
    for span_idx, span_data in model.span_nodes.items():
        span_nodes = span_data['all']
        span_weight = span_weights[span_idx]

        # 计算每个节点的荷载（基于节点数量）
        num_nodes = len(span_nodes)
        nodal_load = span_weight / num_nodes

        # 将荷载应用到每个主梁节点上
        for node in span_nodes:
            if node in total_loads:
                total_loads[node] = (total_loads[node][0], total_loads[node][1], total_loads[node][2] - nodal_load)
            else:
                total_loads[node] = (0, 0, -nodal_load)  # Z方向负值表示向下

    print(f"上部结构恒荷载: {super_weight/1000:.2f} kN")

    # Pier self-weight
    # -----------------------------------
    if model.piers['nodes']:
        for (pier_idx_long, pier_idx_trans), pier_nodes in model.piers['nodes'].items():
            pier_height = model.params.pier_heights[pier_idx_long]

            # Calculate pier volume and weight
            pier_diameter = model.params.pier_section['diameter']
            pier_area = np.pi * (pier_diameter/2)**2
            pier_volume = pier_area * pier_height
            pier_weight = pier_volume * model.params.concrete_density * model.params.gravity

            # Distribute to pier nodes
            num_nodes = len(pier_nodes)
            nodal_load = pier_weight / num_nodes

            for node in pier_nodes:
                if node in total_loads:
                    total_loads[node] = (total_loads[node][0], total_loads[node][1], total_loads[node][2] - nodal_load)
                else:
                    total_loads[node] = (0, 0, -nodal_load)

    # Cap beam self-weight
    # -----------------------------------
    if model.cap_beams['nodes']:
        for _, cap_nodes in model.cap_beams['nodes'].items():
            # Get cap beam cross-section properties
            cap_beam_length = model.params.deck_width
            cap_beam_width = model.params.cap_beam_section['width']
            cap_beam_height = model.params.cap_beam_section['height']

            # Calculate volume and weight
            cap_volume = cap_beam_width * cap_beam_height * cap_beam_length
            cap_weight = cap_volume * model.params.concrete_density * model.params.gravity

            # Distribute to cap beam nodes
            num_nodes = len(cap_nodes)
            nodal_load = cap_weight / num_nodes

            for node in cap_nodes:
                if node in total_loads:
                    total_loads[node] = (total_loads[node][0], total_loads[node][1], total_loads[node][2] - nodal_load)
                else:
                    total_loads[node] = (0, 0, -nodal_load)

    # Apply all accumulated loads
    for node, (fx, fy, fz) in total_loads.items():
        ops.load(node, fx, fy, fz, 0, 0, 0)


def apply_live_load(model):
    """Apply live loads to the bridge deck"""
    # Simplified uniform live load on deck
    live_load_intensity = 5000  # N/m² (5 kPa)

    # 计算总的活荷载
    total_length = sum(model.params.span_lengths)
    deck_width = model.params.deck_width
    total_live_load = live_load_intensity * total_length * deck_width

    # 计算每个跨的活荷载
    span_live_loads = {}
    for span_idx, span_length in enumerate(model.params.span_lengths):
        span_live_loads[span_idx] = (span_length / total_length) * total_live_load

    # 将活荷载分配到主梁节点上
    total_nodes = 0
    for span_idx, span_data in model.span_nodes.items():
        span_nodes = span_data['all']
        span_live_load = span_live_loads[span_idx]

        # 计算每个节点的荷载
        num_nodes = len(span_nodes)
        nodal_load = span_live_load / num_nodes
        total_nodes += num_nodes

        # 将荷载应用到每个主梁节点上
        for node in span_nodes:
            ops.load(node, 0, 0, -nodal_load, 0, 0, 0)

    print(f"上部结构活荷载: {total_live_load/1000:.2f} kN")
